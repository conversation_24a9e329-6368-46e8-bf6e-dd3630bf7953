package com.agricultural.dao;

import com.agricultural.model.OrderItem;
import com.agricultural.model.Item;
import com.agricultural.util.DBUtil;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 订单商品数据访问对象
 * 负责订单商品相关的数据库操作
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class OrderItemDAO {
    
    /**
     * 插入订单商品
     * 
     * @param orderItem 订单商品对象
     * @return 插入成功返回生成的订单商品ID，失败返回null
     */
    public Integer insert(OrderItem orderItem) {
        String sql = "INSERT INTO order_items (order_id, item_id, quantity, price) VALUES (?, ?, ?, ?)";
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
            
            pstmt.setInt(1, orderItem.getOrderId());
            pstmt.setInt(2, orderItem.getItemId());
            pstmt.setInt(3, orderItem.getQuantity());
            pstmt.setBigDecimal(4, orderItem.getPrice());
            
            int affectedRows = pstmt.executeUpdate();
            
            if (affectedRows > 0) {
                rs = pstmt.getGeneratedKeys();
                if (rs.next()) {
                    Integer orderItemId = rs.getInt(1);
                    orderItem.setOrderItemId(orderItemId);
                    return orderItemId;
                }
            }
        } catch (SQLException e) {
            System.err.println("插入订单商品失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, rs);
        }
        
        return null;
    }
    
    /**
     * 根据订单ID查询订单商品
     * 
     * @param orderId 订单ID
     * @return 订单商品列表
     */
    public List<OrderItem> findByOrderId(Integer orderId) {
        String sql = "SELECT oi.*, i.title, i.description, i.category, i.origin, i.unit " +
                    "FROM order_items oi " +
                    "LEFT JOIN items i ON oi.item_id = i.item_id " +
                    "WHERE oi.order_id = ?";
        
        List<OrderItem> orderItems = new ArrayList<>();
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, orderId);
            
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                orderItems.add(mapResultSetToOrderItem(rs));
            }
        } catch (SQLException e) {
            System.err.println("根据订单ID查询订单商品失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, rs);
        }
        
        return orderItems;
    }
    
    /**
     * 批量插入订单商品
     * 
     * @param orderItems 订单商品列表
     * @return 插入成功返回true，失败返回false
     */
    public boolean batchInsert(List<OrderItem> orderItems) {
        String sql = "INSERT INTO order_items (order_id, item_id, quantity, price) VALUES (?, ?, ?, ?)";
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        
        try {
            conn = DBUtil.getConnection();
            conn.setAutoCommit(false); // 开启事务
            
            pstmt = conn.prepareStatement(sql);
            
            for (OrderItem orderItem : orderItems) {
                pstmt.setInt(1, orderItem.getOrderId());
                pstmt.setInt(2, orderItem.getItemId());
                pstmt.setInt(3, orderItem.getQuantity());
                pstmt.setBigDecimal(4, orderItem.getPrice());
                pstmt.addBatch();
            }
            
            int[] results = pstmt.executeBatch();
            conn.commit(); // 提交事务
            
            // 检查是否所有插入都成功
            for (int result : results) {
                if (result <= 0) {
                    return false;
                }
            }
            
            return true;
        } catch (SQLException e) {
            System.err.println("批量插入订单商品失败: " + e.getMessage());
            try {
                if (conn != null) {
                    conn.rollback(); // 回滚事务
                }
            } catch (SQLException rollbackEx) {
                System.err.println("回滚事务失败: " + rollbackEx.getMessage());
            }
        } finally {
            try {
                if (conn != null) {
                    conn.setAutoCommit(true); // 恢复自动提交
                }
            } catch (SQLException e) {
                System.err.println("恢复自动提交失败: " + e.getMessage());
            }
            closeResources(conn, pstmt, null);
        }
        
        return false;
    }
    
    /**
     * 删除订单的所有商品
     * 
     * @param orderId 订单ID
     * @return 删除成功返回true，失败返回false
     */
    public boolean deleteByOrderId(Integer orderId) {
        String sql = "DELETE FROM order_items WHERE order_id = ?";
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, orderId);
            
            int affectedRows = pstmt.executeUpdate();
            return affectedRows >= 0; // 即使没有删除任何记录也算成功
        } catch (SQLException e) {
            System.err.println("删除订单商品失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, null);
        }
        
        return false;
    }
    
    /**
     * 将ResultSet映射为OrderItem对象
     * 
     * @param rs ResultSet对象
     * @return OrderItem对象
     * @throws SQLException SQL异常
     */
    private OrderItem mapResultSetToOrderItem(ResultSet rs) throws SQLException {
        OrderItem orderItem = new OrderItem();
        orderItem.setOrderItemId(rs.getInt("order_item_id"));
        orderItem.setOrderId(rs.getInt("order_id"));
        orderItem.setItemId(rs.getInt("item_id"));
        orderItem.setQuantity(rs.getInt("quantity"));
        orderItem.setPrice(rs.getBigDecimal("price"));
        
        // 设置商品信息
        Item item = new Item();
        item.setItemId(rs.getInt("item_id"));
        item.setTitle(rs.getString("title"));
        item.setDescription(rs.getString("description"));
        item.setCategory(rs.getString("category"));
        item.setOrigin(rs.getString("origin"));
        item.setUnit(rs.getString("unit"));
        orderItem.setItem(item);
        
        return orderItem;
    }
    
    /**
     * 关闭数据库资源
     */
    private void closeResources(Connection conn, PreparedStatement pstmt, ResultSet rs) {
        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
                System.err.println("关闭ResultSet失败: " + e.getMessage());
            }
        }
        if (pstmt != null) {
            try {
                pstmt.close();
            } catch (SQLException e) {
                System.err.println("关闭PreparedStatement失败: " + e.getMessage());
            }
        }
        if (conn != null) {
            try {
                conn.close();
            } catch (SQLException e) {
                System.err.println("关闭Connection失败: " + e.getMessage());
            }
        }
    }
}
