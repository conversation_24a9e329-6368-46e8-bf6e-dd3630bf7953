package com.agricultural.dao;

import com.agricultural.model.CartItem;
import com.agricultural.model.Item;
import com.agricultural.model.User;
import com.agricultural.util.DBUtil;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 购物车数据访问对象
 * 负责购物车相关的数据库操作
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class CartDAO {
    
    /**
     * 添加商品到购物车
     * 
     * @param cartItem 购物车商品对象
     * @return 插入成功返回生成的购物车ID，失败返回null
     */
    public Integer insert(CartItem cartItem) {
        String sql = "INSERT INTO cart_items (user_id, item_id, quantity) VALUES (?, ?, ?) " +
                    "ON DUPLICATE KEY UPDATE quantity = quantity + VALUES(quantity)";
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
            
            pstmt.setInt(1, cartItem.getUserId());
            pstmt.setInt(2, cartItem.getItemId());
            pstmt.setInt(3, cartItem.getQuantity());
            
            int affectedRows = pstmt.executeUpdate();
            
            if (affectedRows > 0) {
                rs = pstmt.getGeneratedKeys();
                if (rs.next()) {
                    Integer cartId = rs.getInt(1);
                    cartItem.setCartId(cartId);
                    return cartId;
                }
            }
        } catch (SQLException e) {
            System.err.println("添加购物车失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, rs);
        }
        
        return null;
    }
    
    /**
     * 根据购物车ID查询购物车商品
     *
     * @param cartId 购物车ID
     * @return 找到返回购物车商品对象，否则返回null
     */
    public CartItem findById(Integer cartId) {
        String sql = "SELECT c.*, i.title, i.description, i.price, i.category, i.origin, i.stock, i.unit, i.status, " +
                    "u.username, u.credit_score " +
                    "FROM cart_items c " +
                    "LEFT JOIN items i ON c.item_id = i.item_id " +
                    "LEFT JOIN users u ON i.seller_id = u.user_id " +
                    "WHERE c.cart_id = ?";

        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;

        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, cartId);

            rs = pstmt.executeQuery();

            if (rs.next()) {
                return mapResultSetToCartItem(rs);
            }
        } catch (SQLException e) {
            System.err.println("根据ID查询购物车商品失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, rs);
        }

        return null;
    }

    /**
     * 根据用户ID查询购物车商品
     *
     * @param userId 用户ID
     * @return 购物车商品列表
     */
    public List<CartItem> findByUserId(Integer userId) {
        String sql = "SELECT c.*, i.title, i.description, i.price, i.category, i.origin, i.stock, i.unit, i.status, " +
                    "u.username, u.credit_score " +
                    "FROM cart_items c " +
                    "LEFT JOIN items i ON c.item_id = i.item_id " +
                    "LEFT JOIN users u ON i.seller_id = u.user_id " +
                    "WHERE c.user_id = ? ORDER BY c.create_time DESC";
        
        List<CartItem> cartItems = new ArrayList<>();
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, userId);
            
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                cartItems.add(mapResultSetToCartItem(rs));
            }
        } catch (SQLException e) {
            System.err.println("查询购物车失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, rs);
        }
        
        return cartItems;
    }
    
    /**
     * 更新购物车商品数量
     * 
     * @param cartId 购物车ID
     * @param quantity 新数量
     * @return 更新成功返回true，失败返回false
     */
    public boolean updateQuantity(Integer cartId, Integer quantity) {
        String sql = "UPDATE cart_items SET quantity = ? WHERE cart_id = ?";
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            
            pstmt.setInt(1, quantity);
            pstmt.setInt(2, cartId);
            
            int affectedRows = pstmt.executeUpdate();
            return affectedRows > 0;
        } catch (SQLException e) {
            System.err.println("更新购物车数量失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, null);
        }
        
        return false;
    }
    
    /**
     * 删除购物车商品
     * 
     * @param cartId 购物车ID
     * @return 删除成功返回true，失败返回false
     */
    public boolean delete(Integer cartId) {
        String sql = "DELETE FROM cart_items WHERE cart_id = ?";
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, cartId);
            
            int affectedRows = pstmt.executeUpdate();
            return affectedRows > 0;
        } catch (SQLException e) {
            System.err.println("删除购物车商品失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, null);
        }
        
        return false;
    }
    
    /**
     * 清空用户购物车
     * 
     * @param userId 用户ID
     * @return 清空成功返回true，失败返回false
     */
    public boolean clearByUserId(Integer userId) {
        String sql = "DELETE FROM cart_items WHERE user_id = ?";
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, userId);
            
            int affectedRows = pstmt.executeUpdate();
            return affectedRows >= 0; // 即使没有删除任何记录也算成功
        } catch (SQLException e) {
            System.err.println("清空购物车失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, null);
        }
        
        return false;
    }
    
    /**
     * 检查商品是否已在购物车中
     * 
     * @param userId 用户ID
     * @param itemId 商品ID
     * @return 存在返回true，不存在返回false
     */
    public boolean existsByUserAndItem(Integer userId, Integer itemId) {
        String sql = "SELECT COUNT(*) FROM cart_items WHERE user_id = ? AND item_id = ?";
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, userId);
            pstmt.setInt(2, itemId);
            
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return rs.getInt(1) > 0;
            }
        } catch (SQLException e) {
            System.err.println("检查购物车商品失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, rs);
        }
        
        return false;
    }
    
    /**
     * 将ResultSet映射为CartItem对象
     * 
     * @param rs ResultSet对象
     * @return CartItem对象
     * @throws SQLException SQL异常
     */
    private CartItem mapResultSetToCartItem(ResultSet rs) throws SQLException {
        CartItem cartItem = new CartItem();
        cartItem.setCartId(rs.getInt("cart_id"));
        cartItem.setUserId(rs.getInt("user_id"));
        cartItem.setItemId(rs.getInt("item_id"));
        cartItem.setQuantity(rs.getInt("quantity"));
        cartItem.setCreateTime(rs.getTimestamp("create_time"));
        
        // 设置商品信息
        Item item = new Item();
        item.setItemId(rs.getInt("item_id"));
        item.setTitle(rs.getString("title"));
        item.setDescription(rs.getString("description"));
        item.setPrice(rs.getBigDecimal("price"));
        item.setCategory(rs.getString("category"));
        item.setOrigin(rs.getString("origin"));
        item.setStock(rs.getInt("stock"));
        item.setUnit(rs.getString("unit"));
        item.setStatus(rs.getInt("status"));
        
        // 设置卖家信息
        String sellerName = rs.getString("username");
        if (sellerName != null) {
            User seller = new User();
            seller.setUsername(sellerName);
            seller.setCreditScore(rs.getInt("credit_score"));
            item.setSeller(seller);
        }
        
        cartItem.setItem(item);
        
        return cartItem;
    }
    
    /**
     * 关闭数据库资源
     */
    private void closeResources(Connection conn, PreparedStatement pstmt, ResultSet rs) {
        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
                System.err.println("关闭ResultSet失败: " + e.getMessage());
            }
        }
        if (pstmt != null) {
            try {
                pstmt.close();
            } catch (SQLException e) {
                System.err.println("关闭PreparedStatement失败: " + e.getMessage());
            }
        }
        if (conn != null) {
            try {
                conn.close();
            } catch (SQLException e) {
                System.err.println("关闭Connection失败: " + e.getMessage());
            }
        }
    }
}
