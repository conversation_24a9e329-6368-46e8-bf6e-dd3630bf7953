<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.agricultural.model.User" %>
<%
    User currentUser = (User) session.getAttribute("currentUser");
    if (currentUser == null) {
        response.sendRedirect("login.jsp");
        return;
    }
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>结算 - 农产品电商平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }

        .nav-links a:hover {
            background-color: rgba(255,255,255,0.2);
        }

        .cart-icon {
            position: relative;
        }

        .cart-count {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .user-info {
            display: none;
            align-items: center;
            gap: 10px;
        }

        .user-name {
            font-weight: bold;
        }

        .auth-links {
            display: flex;
            gap: 10px;
        }

        .credit-score {
            background-color: #f39c12;
            color: white;
            padding: 2px 6px;
            border-radius: 12px;
            font-size: 0.8rem;
        }

        .container {
            max-width: 800px;
            margin: 20px auto;
            padding: 0 20px;
        }

        .checkout-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .checkout-content {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }

        .form-group textarea {
            height: 80px;
            resize: vertical;
        }

        .order-summary {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .total-amount {
            font-size: 1.5rem;
            font-weight: bold;
            color: #e74c3c;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
            margin: 10px;
        }

        .btn-success {
            background-color: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background-color: #229954;
        }

        .btn-secondary {
            background-color: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #7f8c8d;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #c3e6cb;
            margin: 20px 0;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <header class="header">
        <nav class="nav">
            <div class="logo">🌱 农产品电商平台</div>
            <div class="nav-links">
                <a href="index.jsp">首页</a>
                <% if (currentUser != null) { %>
                    <a href="cart.jsp" class="cart-icon">
                        🛒 购物车
                        <span class="cart-count" id="cartCount">0</span>
                    </a>
                    <a href="my-items.jsp">我的商品</a>
                    <a href="my-orders.jsp">我的订单</a>
                    <a href="publish-item.jsp">发布商品</a>
                <% } %>
            </div>
            <div class="user-section">
                <% if (currentUser == null) { %>
                    <!-- 未登录时显示 -->
                    <div class="auth-links">
                        <a href="login.jsp" class="btn btn-primary btn-small">登录</a>
                        <a href="register.jsp" class="btn btn-success btn-small">注册</a>
                    </div>
                <% } else { %>
                    <!-- 已登录时显示 -->
                    <div class="user-info" style="display: flex;">
                        <span class="user-name"><%= currentUser.getUsername() %></span>
                        <span class="credit-score"><%= currentUser.getCreditScore() %>分</span>
                        <a href="javascript:logout()" class="btn btn-primary btn-small">退出</a>
                    </div>
                <% } %>
            </div>
        </nav>
    </header>

    <div class="container">
        <div class="checkout-header">
            <h2>订单结算</h2>
        </div>

        <div id="checkoutForm" class="checkout-content">
            <div id="orderSummary" class="order-summary">
                <div class="loading">正在加载订单信息...</div>
            </div>

            <form id="orderForm">
                <div class="form-group">
                    <label for="shippingAddress">收货地址 *</label>
                    <textarea id="shippingAddress" name="shippingAddress" required 
                              placeholder="请输入详细的收货地址"></textarea>
                </div>

                <div style="text-align: center;">
                    <button type="button" class="btn btn-secondary" onclick="goBack()">返回购物车</button>
                    <button type="submit" class="btn btn-success">立即支付</button>
                </div>
            </form>
        </div>

        <div id="successMessage" class="checkout-content hidden">
            <div class="success-message">
                <h3>🎉 支付成功！</h3>
                <p>您的订单已提交成功，我们会尽快为您安排发货。</p>
                <p>订单号：<span id="orderNumber"></span></p>
            </div>
            <div style="text-align: center;">
                <button class="btn btn-success" onclick="goToOrders()">查看我的订单</button>
                <button class="btn btn-secondary" onclick="goToHome()">继续购物</button>
            </div>
        </div>
    </div>

    <script>
        let cartItems = [];
        let totalAmount = 0;

        // 页面加载时获取购物车数据
        document.addEventListener('DOMContentLoaded', function() {
            loadCartSummary();
            // 验证库存
            validateCartStock();
        });

        // 加载购物车摘要
        function loadCartSummary() {
            fetch('<%=request.getContextPath()%>/cart/list')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data && data.data.length > 0) {
                        cartItems = data.data;
                        displayOrderSummary(cartItems);
                    } else {
                        document.getElementById('orderSummary').innerHTML = 
                            '<div style="text-align: center; color: #666;">购物车为空，请先添加商品</div>';
                    }
                })
                .catch(error => {
                    console.error('加载购物车失败:', error);
                    document.getElementById('orderSummary').innerHTML = 
                        '<div style="text-align: center; color: #e74c3c;">加载失败，请刷新重试</div>';
                });
        }

        // 显示订单摘要
        function displayOrderSummary(items) {
            totalAmount = 0;
            let html = '<h4>订单商品</h4>';
            
            items.forEach(item => {
                const itemInfo = item.item || {};
                const price = itemInfo.price || 0;
                const quantity = item.quantity || 1;
                const subtotal = price * quantity;
                totalAmount += subtotal;

                html += '<div class="summary-item">';
                html += '<span>' + escapeHtml(itemInfo.title || '商品信息不可用') + ' × ' + quantity + '</span>';
                html += '<span>¥' + subtotal.toFixed(2) + '</span>';
                html += '</div>';
            });

            html += '<div class="summary-item total-amount">';
            html += '<span>总计：</span>';
            html += '<span>¥' + totalAmount.toFixed(2) + '</span>';
            html += '</div>';
            
            document.getElementById('orderSummary').innerHTML = html;
        }

        // 提交订单
        document.getElementById('orderForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const shippingAddress = document.getElementById('shippingAddress').value.trim();
            if (!shippingAddress) {
                alert('请输入收货地址');
                return;
            }

            if (cartItems.length === 0) {
                alert('购物车为空');
                return;
            }

            // 先验证库存再创建订单
            validateCartStock(function(isValid, errors) {
                if (!isValid) {
                    alert('购物车中有商品库存不足或不可购买：\n' + errors.join('\n') + '\n\n请返回购物车修改后重试。');
                    return;
                }

                // 创建订单
                fetch('<%=request.getContextPath()%>/order/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'shippingAddress=' + encodeURIComponent(shippingAddress)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 显示成功消息
                        document.getElementById('orderNumber').textContent = data.data.orderId || '未知';
                        document.getElementById('checkoutForm').classList.add('hidden');
                        document.getElementById('successMessage').classList.remove('hidden');
                    } else {
                        alert('订单创建失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('创建订单失败:', error);
                    alert('创建订单失败，请重试');
                });
            });
        });

        // 返回购物车
        function goBack() {
            window.location.href = 'cart.jsp';
        }

        // 查看订单
        function goToOrders() {
            window.location.href = 'my-orders.jsp';
        }

        // 继续购物
        function goToHome() {
            window.location.href = 'index.jsp';
        }

        // 验证购物车库存
        function validateCartStock(callback) {
            fetch('<%=request.getContextPath()%>/cart/validate')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const result = data.data;
                        if (callback) {
                            callback(result.valid, result.errors || []);
                        }

                        // 如果有库存问题，显示警告
                        if (!result.valid && result.errors.length > 0) {
                            const orderSummary = document.getElementById('orderSummary');
                            if (orderSummary) {
                                let warningHtml = '<div style="background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 10px; border-radius: 4px; margin-bottom: 15px;">';
                                warningHtml += '<strong>⚠️ 库存提醒：</strong><br>';
                                warningHtml += result.errors.join('<br>');
                                warningHtml += '</div>';
                                orderSummary.innerHTML = warningHtml + orderSummary.innerHTML;
                            }
                        }
                    } else {
                        console.error('验证库存失败:', data.message);
                        if (callback) {
                            callback(false, ['验证库存失败，请稍后重试']);
                        }
                    }
                })
                .catch(error => {
                    console.error('验证库存失败:', error);
                    if (callback) {
                        callback(false, ['验证库存失败，请稍后重试']);
                    }
                });
        }

        // 工具函数：HTML转义
        function escapeHtml(text) {
            if (!text) return '';
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 退出登录
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                fetch('<%=request.getContextPath()%>/user/logout')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.href = 'index.jsp';
                        } else {
                            alert('退出失败: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('退出失败:', error);
                        alert('退出失败，请稍后重试');
                    });
            }
        }
    </script>
</body>
</html>
