package com.agricultural.model;

import java.math.BigDecimal;

/**
 * 订单商品实体类
 * 对应数据库中的order_items表
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class OrderItem {
    
    // 订单商品ID，主键
    private Integer orderItemId;
    
    // 订单ID，外键关联订单表
    private Integer orderId;
    
    // 商品ID，外键关联商品表
    private Integer itemId;
    
    // 购买数量
    private Integer quantity;
    
    // 商品单价
    private BigDecimal price;
    
    // 关联对象（用于查询时填充）
    private Item item;      // 商品信息
    
    /**
     * 无参构造函数
     */
    public OrderItem() {
    }
    
    /**
     * 带参构造函数
     * 
     * @param orderId 订单ID
     * @param itemId 商品ID
     * @param quantity 购买数量
     * @param price 商品单价
     */
    public OrderItem(Integer orderId, Integer itemId, Integer quantity, BigDecimal price) {
        this.orderId = orderId;
        this.itemId = itemId;
        this.quantity = quantity;
        this.price = price;
    }
    
    // Getter和Setter方法
    
    public Integer getOrderItemId() {
        return orderItemId;
    }
    
    public void setOrderItemId(Integer orderItemId) {
        this.orderItemId = orderItemId;
    }
    
    public Integer getOrderId() {
        return orderId;
    }
    
    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }
    
    public Integer getItemId() {
        return itemId;
    }
    
    public void setItemId(Integer itemId) {
        this.itemId = itemId;
    }
    
    public Integer getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }
    
    public BigDecimal getPrice() {
        return price;
    }
    
    public void setPrice(BigDecimal price) {
        this.price = price;
    }
    
    public Item getItem() {
        return item;
    }
    
    public void setItem(Item item) {
        this.item = item;
    }
    
    /**
     * 计算小计金额
     * 
     * @return 小计金额
     */
    public BigDecimal getSubtotal() {
        if (price != null && quantity != null) {
            return price.multiply(new BigDecimal(quantity));
        }
        return BigDecimal.ZERO;
    }
    
    /**
     * 重写toString方法，方便调试
     */
    @Override
    public String toString() {
        return "OrderItem{" +
                "orderItemId=" + orderItemId +
                ", orderId=" + orderId +
                ", itemId=" + itemId +
                ", quantity=" + quantity +
                ", price=" + price +
                '}';
    }
    
    /**
     * 重写equals方法，用于对象比较
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        OrderItem orderItem = (OrderItem) obj;
        return orderItemId != null ? orderItemId.equals(orderItem.orderItemId) : orderItem.orderItemId == null;
    }
    
    /**
     * 重写hashCode方法
     */
    @Override
    public int hashCode() {
        return orderItemId != null ? orderItemId.hashCode() : 0;
    }
}
