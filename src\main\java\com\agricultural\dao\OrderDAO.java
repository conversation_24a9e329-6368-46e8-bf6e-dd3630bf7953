package com.agricultural.dao;

import com.agricultural.model.Order;
import com.agricultural.model.OrderItem;
import com.agricultural.model.User;
import com.agricultural.util.DBUtil;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 订单数据访问对象
 * 负责订单相关的数据库操作
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class OrderDAO {
    
    /**
     * 插入新订单
     * 
     * @param order 订单对象
     * @return 插入成功返回生成的订单ID，失败返回null
     */
    public Integer insert(Order order) {
        String sql = "INSERT INTO orders (buyer_id, seller_id, status, total_price, shipping_address) VALUES (?, ?, ?, ?, ?)";
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
            
            // 设置参数
            pstmt.setInt(1, order.getBuyerId());
            pstmt.setInt(2, order.getSellerId());
            pstmt.setInt(3, order.getStatus() != null ? order.getStatus() : Order.STATUS_PENDING);
            pstmt.setBigDecimal(4, order.getTotalPrice());
            pstmt.setString(5, order.getShippingAddress());
            
            // 执行插入
            int affectedRows = pstmt.executeUpdate();
            
            if (affectedRows > 0) {
                // 获取生成的主键
                rs = pstmt.getGeneratedKeys();
                if (rs.next()) {
                    Integer orderId = rs.getInt(1);
                    order.setOrderId(orderId);
                    return orderId;
                }
            }
        } catch (SQLException e) {
            System.err.println("插入订单失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, rs);
        }
        
        return null;
    }
    
    /**
     * 根据订单ID查找订单
     * 
     * @param orderId 订单ID
     * @return 找到返回订单对象，否则返回null
     */
    public Order findById(Integer orderId) {
        String sql = "SELECT o.*, " +
                    "b.username as buyer_name, b.credit_score as buyer_credit, " +
                    "s.username as seller_name, s.credit_score as seller_credit " +
                    "FROM orders o " +
                    "LEFT JOIN users b ON o.buyer_id = b.user_id " +
                    "LEFT JOIN users s ON o.seller_id = s.user_id " +
                    "WHERE o.order_id = ?";
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, orderId);
            
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return mapResultSetToOrder(rs);
            }
        } catch (SQLException e) {
            System.err.println("根据ID查询订单失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, rs);
        }
        
        return null;
    }
    
    /**
     * 根据买家ID查询订单
     * 
     * @param buyerId 买家ID
     * @return 订单列表
     */
    public List<Order> findByBuyerId(Integer buyerId) {
        String sql = "SELECT o.*, " +
                    "b.username as buyer_name, b.credit_score as buyer_credit, " +
                    "s.username as seller_name, s.credit_score as seller_credit " +
                    "FROM orders o " +
                    "LEFT JOIN users b ON o.buyer_id = b.user_id " +
                    "LEFT JOIN users s ON o.seller_id = s.user_id " +
                    "WHERE o.buyer_id = ? ORDER BY o.create_time DESC";
        
        List<Order> orders = new ArrayList<>();
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, buyerId);
            
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                orders.add(mapResultSetToOrder(rs));
            }
        } catch (SQLException e) {
            System.err.println("根据买家ID查询订单失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, rs);
        }
        
        return orders;
    }
    
    /**
     * 根据卖家ID查询订单
     * 
     * @param sellerId 卖家ID
     * @return 订单列表
     */
    public List<Order> findBySellerId(Integer sellerId) {
        String sql = "SELECT o.*, " +
                    "b.username as buyer_name, b.credit_score as buyer_credit, " +
                    "s.username as seller_name, s.credit_score as seller_credit " +
                    "FROM orders o " +
                    "LEFT JOIN users b ON o.buyer_id = b.user_id " +
                    "LEFT JOIN users s ON o.seller_id = s.user_id " +
                    "WHERE o.seller_id = ? ORDER BY o.create_time DESC";
        
        List<Order> orders = new ArrayList<>();
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, sellerId);
            
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                orders.add(mapResultSetToOrder(rs));
            }
        } catch (SQLException e) {
            System.err.println("根据卖家ID查询订单失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, rs);
        }
        
        return orders;
    }
    
    /**
     * 更新订单状态
     * 
     * @param orderId 订单ID
     * @param status 新状态
     * @return 更新成功返回true，失败返回false
     */
    public boolean updateStatus(Integer orderId, Integer status) {
        String sql = "UPDATE orders SET status = ? WHERE order_id = ?";
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            
            pstmt.setInt(1, status);
            pstmt.setInt(2, orderId);
            
            int affectedRows = pstmt.executeUpdate();
            return affectedRows > 0;
        } catch (SQLException e) {
            System.err.println("更新订单状态失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, null);
        }
        
        return false;
    }
    
    /**
     * 更新物流信息
     * 
     * @param orderId 订单ID
     * @param logisticsCompany 物流公司
     * @param trackingNumber 物流单号
     * @param logisticsStatus 物流状态
     * @return 更新成功返回true，失败返回false
     */
    public boolean updateLogistics(Integer orderId, String logisticsCompany, String trackingNumber, String logisticsStatus) {
        String sql = "UPDATE orders SET logistics_company = ?, tracking_number = ?, logistics_status = ? WHERE order_id = ?";
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            
            pstmt.setString(1, logisticsCompany);
            pstmt.setString(2, trackingNumber);
            pstmt.setString(3, logisticsStatus);
            pstmt.setInt(4, orderId);
            
            int affectedRows = pstmt.executeUpdate();
            return affectedRows > 0;
        } catch (SQLException e) {
            System.err.println("更新物流信息失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, null);
        }
        
        return false;
    }
    
    /**
     * 查询所有订单
     * 
     * @return 订单列表
     */
    public List<Order> findAll() {
        String sql = "SELECT o.*, " +
                    "b.username as buyer_name, b.credit_score as buyer_credit, " +
                    "s.username as seller_name, s.credit_score as seller_credit " +
                    "FROM orders o " +
                    "LEFT JOIN users b ON o.buyer_id = b.user_id " +
                    "LEFT JOIN users s ON o.seller_id = s.user_id " +
                    "ORDER BY o.create_time DESC";
        
        List<Order> orders = new ArrayList<>();
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                orders.add(mapResultSetToOrder(rs));
            }
        } catch (SQLException e) {
            System.err.println("查询所有订单失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, rs);
        }
        
        return orders;
    }
    
    /**
     * 将ResultSet映射为Order对象
     * 
     * @param rs ResultSet对象
     * @return Order对象
     * @throws SQLException SQL异常
     */
    private Order mapResultSetToOrder(ResultSet rs) throws SQLException {
        Order order = new Order();
        order.setOrderId(rs.getInt("order_id"));
        order.setBuyerId(rs.getInt("buyer_id"));
        order.setSellerId(rs.getInt("seller_id"));
        order.setStatus(rs.getInt("status"));
        order.setTotalPrice(rs.getBigDecimal("total_price"));
        order.setShippingAddress(rs.getString("shipping_address"));
        order.setLogisticsStatus(rs.getString("logistics_status"));
        order.setLogisticsCompany(rs.getString("logistics_company"));
        order.setTrackingNumber(rs.getString("tracking_number"));
        order.setCreateTime(rs.getTimestamp("create_time"));
        order.setUpdateTime(rs.getTimestamp("update_time"));
        
        // 设置买家信息
        String buyerName = rs.getString("buyer_name");
        if (buyerName != null) {
            User buyer = new User();
            buyer.setUserId(order.getBuyerId());
            buyer.setUsername(buyerName);
            buyer.setCreditScore(rs.getInt("buyer_credit"));
            order.setBuyer(buyer);
        }
        
        // 设置卖家信息
        String sellerName = rs.getString("seller_name");
        if (sellerName != null) {
            User seller = new User();
            seller.setUserId(order.getSellerId());
            seller.setUsername(sellerName);
            seller.setCreditScore(rs.getInt("seller_credit"));
            order.setSeller(seller);
        }
        
        return order;
    }
    
    /**
     * 关闭数据库资源
     */
    private void closeResources(Connection conn, PreparedStatement pstmt, ResultSet rs) {
        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
                System.err.println("关闭ResultSet失败: " + e.getMessage());
            }
        }
        if (pstmt != null) {
            try {
                pstmt.close();
            } catch (SQLException e) {
                System.err.println("关闭PreparedStatement失败: " + e.getMessage());
            }
        }
        if (conn != null) {
            try {
                conn.close();
            } catch (SQLException e) {
                System.err.println("关闭Connection失败: " + e.getMessage());
            }
        }
    }
}
