package com.agricultural.util;

import com.google.gson.Gson;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Map;

/**
 * HTTP响应工具类
 * 提供统一的响应格式和便捷的响应方法
 *
 * <AUTHOR>
 * @version 1.0
 */
public class ResponseUtil {

    // JSON处理器
    private static final Gson gson = new Gson();

    /**
     * 发送JSON格式的成功响应
     *
     * @param response HTTP响应对象
     * @param data 响应数据
     * @throws IOException IO异常
     */
    public static void sendJsonSuccess(HttpServletResponse response, Object data) throws IOException {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "操作成功");
        result.put("data", data);

        sendJson(response, result);
    }

    /**
     * 发送JSON格式的成功响应（带自定义消息）
     *
     * @param response HTTP响应对象
     * @param message 成功消息
     * @param data 响应数据
     * @throws IOException IO异常
     */
    public static void sendJsonSuccess(HttpServletResponse response, String message, Object data) throws IOException {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", message);
        result.put("data", data);

        sendJson(response, result);
    }

    /**
     * 发送JSON格式的错误响应
     *
     * @param response HTTP响应对象
     * @param message 错误消息
     * @throws IOException IO异常
     */
    public static void sendJsonError(HttpServletResponse response, String message) throws IOException {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", message);
        result.put("data", null);

        sendJson(response, result);
    }

    /**
     * 发送JSON格式的错误响应（带错误码）
     *
     * @param response HTTP响应对象
     * @param code 错误码
     * @param message 错误消息
     * @throws IOException IO异常
     */
    public static void sendJsonError(HttpServletResponse response, int code, String message) throws IOException {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("code", code);
        result.put("message", message);
        result.put("data", null);

        response.setStatus(code);
        sendJson(response, result);
    }

    /**
     * 发送JSON响应的通用方法
     *
     * @param response HTTP响应对象
     * @param data 要转换为JSON的数据
     * @throws IOException IO异常
     */
    public static void sendJson(HttpServletResponse response, Object data) throws IOException {
        // 强制设置字符编码
        response.setCharacterEncoding("UTF-8");

        // 设置响应头
        response.setContentType("application/json;charset=UTF-8");
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Pragma", "no-cache");

        // 允许跨域（如果需要）
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE");
        response.setHeader("Access-Control-Allow-Headers", "Content-Type");

        // 写入JSON数据
        PrintWriter out = response.getWriter();
        String jsonString = gson.toJson(data);
        out.print(jsonString);
        out.flush();
        out.close();
    }

    /**
     * 发送HTML响应
     *
     * @param response HTTP响应对象
     * @param html HTML内容
     * @throws IOException IO异常
     */
    public static void sendHtml(HttpServletResponse response, String html) throws IOException {
        // 强制设置字符编码
        response.setCharacterEncoding("UTF-8");
        response.setContentType("text/html;charset=UTF-8");

        PrintWriter out = response.getWriter();
        out.print(html);
        out.flush();
        out.close();
    }

    /**
     * 发送重定向响应
     *
     * @param response HTTP响应对象
     * @param url 重定向URL
     * @throws IOException IO异常
     */
    public static void sendRedirect(HttpServletResponse response, String url) throws IOException {
        response.sendRedirect(url);
    }

    /**
     * 发送简单的成功页面
     *
     * @param response HTTP响应对象
     * @param message 成功消息
     * @param redirectUrl 跳转URL（可选）
     * @throws IOException IO异常
     */
    public static void sendSuccessPage(HttpServletResponse response, String message, String redirectUrl) throws IOException {
        StringBuilder html = new StringBuilder();
        html.append("<!DOCTYPE html>");
        html.append("<html><head>");
        html.append("<meta charset='UTF-8'>");
        html.append("<title>操作成功</title>");
        html.append("<style>body{font-family:Arial;text-align:center;margin-top:100px;}</style>");
        html.append("</head><body>");
        html.append("<h2 style='color:green;'>✓ ").append(message).append("</h2>");

        if (redirectUrl != null && !redirectUrl.isEmpty()) {
            html.append("<p>页面将在3秒后自动跳转...</p>");
            html.append("<script>setTimeout(function(){window.location.href='").append(redirectUrl).append("';}, 3000);</script>");
        }

        html.append("</body></html>");

        sendHtml(response, html.toString());
    }

    /**
     * 发送简单的错误页面
     *
     * @param response HTTP响应对象
     * @param message 错误消息
     * @param backUrl 返回URL（可选）
     * @throws IOException IO异常
     */
    public static void sendErrorPage(HttpServletResponse response, String message, String backUrl) throws IOException {
        StringBuilder html = new StringBuilder();
        html.append("<!DOCTYPE html>");
        html.append("<html><head>");
        html.append("<meta charset='UTF-8'>");
        html.append("<title>操作失败</title>");
        html.append("<style>body{font-family:Arial;text-align:center;margin-top:100px;}</style>");
        html.append("</head><body>");
        html.append("<h2 style='color:red;'>✗ ").append(message).append("</h2>");

        if (backUrl != null && !backUrl.isEmpty()) {
            html.append("<p><a href='").append(backUrl).append("'>返回上一页</a></p>");
        }

        html.append("</body></html>");

        sendHtml(response, html.toString());
    }
}
