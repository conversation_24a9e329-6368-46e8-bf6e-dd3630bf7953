-- 农产品电商平台数据库设计
-- 创建数据库
CREATE DATABASE IF NOT EXISTS agricultural_ecommerce DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE agricultural_ecommerce;

-- 删除已存在的表（按依赖关系倒序删除）
DROP TABLE IF EXISTS reviews;
DROP TABLE IF EXISTS order_items;
DROP TABLE IF EXISTS orders;
DROP TABLE IF EXISTS cart_items;
DROP TABLE IF EXISTS items;
DROP TABLE IF EXISTS users;

-- 1. 用户表
CREATE TABLE users (
    user_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID，主键',
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名，唯一',
    password VARCHAR(64) NOT NULL COMMENT '密码，MD5加密存储',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    role TINYINT DEFAULT 1 COMMENT '用户角色：1-买家，2-卖家，3-管理员',
    address TEXT COMMENT '收货地址',
    credit_score INT DEFAULT 100 COMMENT '信誉分，初始值100',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 2. 商品表
CREATE TABLE items (
    item_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '商品ID，主键',
    seller_id INT NOT NULL COMMENT '卖家ID，外键关联用户表',
    title VARCHAR(100) NOT NULL COMMENT '商品标题',
    description TEXT COMMENT '商品描述',
    price DECIMAL(10, 2) NOT NULL COMMENT '商品价格',
    category VARCHAR(50) DEFAULT '其他' COMMENT '商品分类：蔬菜、水果、粮食、其他',
    origin VARCHAR(100) COMMENT '产地',
    stock INT DEFAULT 1 COMMENT '库存数量',
    unit VARCHAR(20) DEFAULT '件' COMMENT '计量单位：件、斤、公斤等',
    status TINYINT DEFAULT 1 COMMENT '商品状态：1-上架，2-下架，3-缺货',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (seller_id) REFERENCES users(user_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

-- 3. 购物车表
CREATE TABLE cart_items (
    cart_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '购物车ID，主键',
    user_id INT NOT NULL COMMENT '用户ID，外键关联用户表',
    item_id INT NOT NULL COMMENT '商品ID，外键关联商品表',
    quantity INT DEFAULT 1 COMMENT '商品数量',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (item_id) REFERENCES items(item_id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_item (user_id, item_id) COMMENT '同一用户同一商品只能有一条记录'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='购物车表';

-- 4. 订单表
CREATE TABLE orders (
    order_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '订单ID，主键',
    buyer_id INT NOT NULL COMMENT '买家ID，外键关联用户表',
    seller_id INT NOT NULL COMMENT '卖家ID，外键关联用户表',
    status TINYINT DEFAULT 1 COMMENT '订单状态：1-待支付，2-已支付，3-已发货，4-已完成，5-已取消',
    total_price DECIMAL(10, 2) NOT NULL COMMENT '订单总价',
    shipping_address TEXT COMMENT '收货地址',
    logistics_status VARCHAR(100) COMMENT '物流状态',
    logistics_company VARCHAR(50) COMMENT '物流公司',
    tracking_number VARCHAR(100) COMMENT '物流单号',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (buyer_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (seller_id) REFERENCES users(user_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- 5. 订单商品表
CREATE TABLE order_items (
    order_item_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '订单商品ID，主键',
    order_id INT NOT NULL COMMENT '订单ID，外键关联订单表',
    item_id INT NOT NULL COMMENT '商品ID，外键关联商品表',
    quantity INT NOT NULL COMMENT '购买数量',
    price DECIMAL(10, 2) NOT NULL COMMENT '商品单价',
    FOREIGN KEY (order_id) REFERENCES orders(order_id) ON DELETE CASCADE,
    FOREIGN KEY (item_id) REFERENCES items(item_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单商品表';

-- 6. 评价表
CREATE TABLE reviews (
    review_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '评价ID，主键',
    order_id INT UNIQUE NOT NULL COMMENT '订单ID，外键关联订单表，唯一约束',
    buyer_id INT NOT NULL COMMENT '买家ID，外键关联用户表',
    seller_id INT NOT NULL COMMENT '卖家ID，外键关联用户表',
    score TINYINT NOT NULL COMMENT '评分：1-5分',
    comment TEXT COMMENT '评价内容',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '评价时间',
    FOREIGN KEY (order_id) REFERENCES orders(order_id) ON DELETE CASCADE,
    FOREIGN KEY (buyer_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (seller_id) REFERENCES users(user_id) ON DELETE CASCADE,
    CHECK (score BETWEEN 1 AND 5)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评价表';

-- 创建索引提高查询性能
CREATE INDEX idx_items_seller_id ON items(seller_id);
CREATE INDEX idx_items_status ON items(status);
CREATE INDEX idx_items_category ON items(category);
CREATE INDEX idx_cart_items_user_id ON cart_items(user_id);
CREATE INDEX idx_orders_buyer_id ON orders(buyer_id);
CREATE INDEX idx_orders_seller_id ON orders(seller_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_order_items_order_id ON order_items(order_id);
CREATE INDEX idx_reviews_seller_id ON reviews(seller_id);

-- 插入测试数据
-- 插入测试用户
INSERT INTO users (username, password, email, phone, role, address) VALUES
('admin', MD5('123456'), '<EMAIL>', '13800138000', 3, '管理员地址'),
('farmer1', MD5('123456'), '<EMAIL>', '13800138001', 2, '山东省寿光市蔬菜基地'),
('farmer2', MD5('123456'), '<EMAIL>', '13800138002', 2, '新疆维吾尔自治区阿克苏地区'),
('buyer1', MD5('123456'), '<EMAIL>', '13800138003', 1, '北京市朝阳区某小区');

-- 插入测试商品
INSERT INTO items (seller_id, title, description, price, category, origin, stock, unit, status) VALUES
(2, '新鲜西红柿', '大棚种植，无农药残留，口感佳', 8.50, '蔬菜', '山东寿光', 100, '斤', 1),
(2, '有机黄瓜', '有机认证，脆嫩多汁', 6.00, '蔬菜', '山东寿光', 80, '斤', 1),
(3, '阿克苏苹果', '冰糖心苹果，甜度高', 12.00, '水果', '新疆阿克苏', 200, '斤', 1),
(3, '新疆大枣', '和田大枣，营养丰富', 25.00, '水果', '新疆和田', 50, '斤', 1);

-- 插入测试订单
INSERT INTO orders (buyer_id, seller_id, status, total_price, shipping_address) VALUES
(4, 2, 4, 42.50, '北京市朝阳区某小区'),
(4, 3, 2, 60.00, '北京市朝阳区某小区');

-- 插入订单商品
INSERT INTO order_items (order_id, item_id, quantity, price) VALUES
(1, 1, 5, 8.50),
(2, 3, 5, 12.00);

-- 插入测试评价
INSERT INTO reviews (order_id, buyer_id, seller_id, score, comment) VALUES
(1, 4, 2, 5, '西红柿很新鲜，包装也很好！'),
(2, 4, 3, 4, '苹果很甜，下次还会买');

-- 创建触发器：自动更新用户信誉分
DELIMITER $$
CREATE TRIGGER update_credit_score_after_review
AFTER INSERT ON reviews
FOR EACH ROW
BEGIN
    DECLARE avg_score DECIMAL(3,2);
    
    -- 计算卖家的平均评分
    SELECT AVG(score) INTO avg_score
    FROM reviews
    WHERE seller_id = NEW.seller_id;
    
    -- 更新卖家的信誉分（基础分100 + 平均分*20）
    UPDATE users 
    SET credit_score = 100 + ROUND(avg_score * 20)
    WHERE user_id = NEW.seller_id;
END$$
DELIMITER ;
