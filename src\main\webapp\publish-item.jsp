<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.agricultural.model.User" %>
<%
    // 检查是否已登录
    User currentUser = (User) session.getAttribute("currentUser");
    if (currentUser == null) {
        response.sendRedirect("login.jsp?redirect=" + java.net.URLEncoder.encode(request.getRequestURL().toString(), "UTF-8"));
        return;
    }

    // 检查是否为卖家或管理员
    if (currentUser.getRole() != User.ROLE_SELLER && currentUser.getRole() != User.ROLE_ADMIN) {
        response.sendRedirect("index.jsp");
        return;
    }
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发布商品 - 农产品电商平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            padding: 8px 16px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }

        .nav-links a:hover {
            background-color: rgba(255,255,255,0.1);
        }

        .container {
            max-width: 800px;
            margin: 20px auto;
            padding: 0 20px;
        }

        .publish-form {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .form-title {
            font-size: 1.5rem;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }

        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-help {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .btn-primary {
            background-color: #27ae60;
            color: white;
        }

        .btn-primary:hover {
            background-color: #229954;
        }

        .btn-primary:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }

        .btn-secondary {
            background-color: #95a5a6;
            color: white;
            margin-right: 10px;
        }

        .btn-secondary:hover {
            background-color: #7f8c8d;
        }

        .form-actions {
            text-align: center;
            margin-top: 30px;
        }

        .error-message {
            background-color: #e74c3c;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }

        .success-message {
            background-color: #27ae60;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }

        .loading {
            display: none;
            text-align: center;
            color: #666;
            margin: 20px 0;
        }

        @media (max-width: 768px) {
            .nav {
                flex-direction: column;
                gap: 10px;
            }

            .container {
                margin: 10px;
                padding: 0 10px;
            }

            .publish-form {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <header class="header">
        <nav class="nav">
            <div class="logo">🌱 农产品电商平台</div>
            <div class="nav-links">
                <a href="index.jsp">首页</a>
                <a href="cart.jsp">购物车</a>
                <a href="my-items.jsp">我的商品</a>
                <a href="my-orders.jsp">我的订单</a>
                <span>欢迎，<%= currentUser.getUsername() %></span>
                <a href="<%=request.getContextPath()%>/user/logout">退出</a>
            </div>
        </nav>
    </header>

    <!-- 主要内容 -->
    <main class="container">
        <div class="publish-form">
            <h1 class="form-title">发布商品</h1>

            <div id="errorMessage" class="error-message"></div>
            <div id="successMessage" class="success-message"></div>
            <div id="loading" class="loading">正在发布商品...</div>

            <form id="publishForm" onsubmit="handlePublish(event)">
                <div class="form-group">
                    <label for="title" class="form-label">商品标题 *</label>
                    <input type="text" id="title" name="title" class="form-input" required maxlength="100">
                    <div class="form-help">请输入简洁明了的商品标题，最多100个字符</div>
                </div>

                <div class="form-group">
                    <label for="description" class="form-label">商品描述</label>
                    <textarea id="description" name="description" class="form-input form-textarea"
                              placeholder="请详细描述商品的状况、购买时间、使用情况等..."></textarea>
                    <div class="form-help">详细的描述有助于买家了解商品</div>
                </div>

                <div class="form-group">
                    <label for="category" class="form-label">商品分类 *</label>
                    <select id="category" name="category" class="form-input" required>
                        <option value="">请选择分类</option>
                        <option value="蔬菜">蔬菜</option>
                        <option value="水果">水果</option>
                        <option value="粮食">粮食</option>
                        <option value="其他">其他</option>
                    </select>
                    <div class="form-help">请选择农产品分类</div>
                </div>

                <div class="form-group">
                    <label for="origin" class="form-label">产地</label>
                    <input type="text" id="origin" name="origin" class="form-input" placeholder="如：山东寿光">
                    <div class="form-help">请输入农产品产地</div>
                </div>

                <div class="form-group">
                    <label for="price" class="form-label">商品价格 *</label>
                    <input type="number" id="price" name="price" class="form-input" required
                           min="0.01" step="0.01" placeholder="0.00">
                    <div class="form-help">请输入合理的价格，单位：元</div>
                </div>

                <div class="form-group">
                    <label for="stock" class="form-label">库存数量 *</label>
                    <input type="number" id="stock" name="stock" class="form-input" required
                           min="1" value="1">
                    <div class="form-help">请输入库存数量</div>
                </div>

                <div class="form-group">
                    <label for="unit" class="form-label">计量单位 *</label>
                    <select id="unit" name="unit" class="form-input" required>
                        <option value="件">件</option>
                        <option value="斤">斤</option>
                        <option value="公斤">公斤</option>
                        <option value="箱">箱</option>
                        <option value="袋">袋</option>
                    </select>
                    <div class="form-help">请选择计量单位</div>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="goBack()">取消</button>
                    <button type="submit" class="btn btn-primary" id="publishBtn">发布商品</button>
                </div>
            </form>
        </div>
    </main>

    <script>
        // 处理发布表单提交
        function handlePublish(event) {
            event.preventDefault();

            const title = document.getElementById('title').value.trim();
            const description = document.getElementById('description').value.trim();
            const category = document.getElementById('category').value;
            const origin = document.getElementById('origin').value.trim();
            const price = document.getElementById('price').value;
            const stock = document.getElementById('stock').value;
            const unit = document.getElementById('unit').value;

            // 基本验证
            if (!title) {
                showErrorMessage('请输入商品标题');
                return;
            }

            if (!category) {
                showErrorMessage('请选择商品分类');
                return;
            }

            if (!price || parseFloat(price) <= 0) {
                showErrorMessage('请输入有效的商品价格');
                return;
            }

            if (!stock || parseInt(stock) <= 0) {
                showErrorMessage('请输入有效的库存数量');
                return;
            }

            // 显示加载状态
            showLoading(true);
            hideMessages();

            // 发送发布请求
            fetch('<%=request.getContextPath()%>/item/publish', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'title=' + encodeURIComponent(title) +
                      '&description=' + encodeURIComponent(description) +
                      '&category=' + encodeURIComponent(category) +
                      '&origin=' + encodeURIComponent(origin) +
                      '&price=' + encodeURIComponent(price) +
                      '&stock=' + encodeURIComponent(stock) +
                      '&unit=' + encodeURIComponent(unit)
            })
            .then(response => response.json())
            .then(data => {
                showLoading(false);

                if (data.success) {
                    showSuccessMessage('商品发布成功！正在跳转...');

                    // 延迟跳转到我的商品页面
                    setTimeout(() => {
                        window.location.href = 'my-items.jsp';
                    }, 1500);
                } else {
                    showErrorMessage(data.message || '发布失败，请重试');
                }
            })
            .catch(error => {
                showLoading(false);
                console.error('发布请求失败:', error);
                showErrorMessage('发布失败，请检查网络连接');
            });
        }

        // 返回上一页
        function goBack() {
            if (confirm('确定要取消发布吗？已填写的内容将丢失。')) {
                window.history.back();
            }
        }

        // 显示错误消息
        function showErrorMessage(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';

            // 3秒后自动隐藏
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 3000);
        }

        // 显示成功消息
        function showSuccessMessage(message) {
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
        }

        // 隐藏所有消息
        function hideMessages() {
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('successMessage').style.display = 'none';
        }

        // 显示/隐藏加载状态
        function showLoading(show) {
            const loadingDiv = document.getElementById('loading');
            const publishBtn = document.getElementById('publishBtn');

            if (show) {
                loadingDiv.style.display = 'block';
                publishBtn.disabled = true;
                publishBtn.textContent = '发布中...';
            } else {
                loadingDiv.style.display = 'none';
                publishBtn.disabled = false;
                publishBtn.textContent = '发布商品';
            }
        }

        // 价格输入验证
        document.getElementById('price').addEventListener('input', function() {
            const value = parseFloat(this.value);
            if (value < 0) {
                this.value = '';
            }
        });

        // 标题长度限制提示
        document.getElementById('title').addEventListener('input', function() {
            const maxLength = 100;
            const currentLength = this.value.length;
            const helpText = this.parentNode.querySelector('.form-help');

            if (currentLength > maxLength - 10) {
                helpText.textContent = '还可以输入 ' + (maxLength - currentLength) + ' 个字符';
                helpText.style.color = currentLength >= maxLength ? '#e74c3c' : '#f39c12';
            } else {
                helpText.textContent = '请输入简洁明了的商品标题，最多100个字符';
                helpText.style.color = '#666';
            }
        });
    </script>
</body>
</html>
