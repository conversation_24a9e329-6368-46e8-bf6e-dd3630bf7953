package com.agricultural.service;

import com.agricultural.dao.UserDAO;
import com.agricultural.model.User;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.List;

/**
 * 用户业务逻辑服务类
 * 处理用户相关的业务逻辑
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class UserService {
    
    private UserDAO userDAO;
    
    /**
     * 构造函数
     */
    public UserService() {
        this.userDAO = new UserDAO();
    }
    
    /**
     * 用户注册
     * 
     * @param username 用户名
     * @param password 密码（明文）
     * @param email 邮箱
     * @param phone 手机号
     * @param role 用户角色
     * @param address 地址
     * @return 注册成功返回用户对象，失败返回null
     */
    public User register(String username, String password, String email, String phone, Integer role, String address) {
        // 参数验证
        if (username == null || username.trim().isEmpty()) {
            throw new IllegalArgumentException("用户名不能为空");
        }
        if (password == null || password.trim().isEmpty()) {
            throw new IllegalArgumentException("密码不能为空");
        }
        if (email == null || email.trim().isEmpty()) {
            throw new IllegalArgumentException("邮箱不能为空");
        }
        
        // 检查用户名是否已存在
        if (userDAO.findByUsername(username.trim()) != null) {
            throw new IllegalArgumentException("用户名已存在");
        }
        
        // 密码加密
        String encryptedPassword = encryptPassword(password);
        
        // 创建用户对象
        User user = new User(username.trim(), encryptedPassword, email.trim(), phone);
        if (role != null) {
            user.setRole(role);
        }
        if (address != null && !address.trim().isEmpty()) {
            user.setAddress(address.trim());
        }
        
        // 保存到数据库
        Integer userId = userDAO.insert(user);
        
        if (userId != null) {
            user.setUserId(userId);
            return user;
        }
        
        return null;
    }
    
    /**
     * 用户登录
     * 
     * @param username 用户名
     * @param password 密码（明文）
     * @return 登录成功返回用户对象，失败返回null
     */
    public User login(String username, String password) {
        // 参数验证
        if (username == null || username.trim().isEmpty()) {
            throw new IllegalArgumentException("用户名不能为空");
        }
        if (password == null || password.trim().isEmpty()) {
            throw new IllegalArgumentException("密码不能为空");
        }
        
        // 密码加密
        String encryptedPassword = encryptPassword(password);
        
        // 查询用户
        User user = userDAO.findByUsernameAndPassword(username.trim(), encryptedPassword);
        
        return user;
    }
    
    /**
     * 根据用户ID获取用户信息
     * 
     * @param userId 用户ID
     * @return 用户对象，不存在返回null
     */
    public User getUserById(Integer userId) {
        if (userId == null || userId <= 0) {
            return null;
        }
        
        return userDAO.findById(userId);
    }
    
    /**
     * 根据用户名获取用户信息
     * 
     * @param username 用户名
     * @return 用户对象，不存在返回null
     */
    public User getUserByUsername(String username) {
        if (username == null || username.trim().isEmpty()) {
            return null;
        }
        
        return userDAO.findByUsername(username.trim());
    }
    
    /**
     * 更新用户信息
     * 
     * @param user 用户对象
     * @return 更新成功返回true，失败返回false
     */
    public boolean updateUser(User user) {
        if (user == null || user.getUserId() == null) {
            throw new IllegalArgumentException("用户信息不完整");
        }
        
        return userDAO.update(user);
    }
    
    /**
     * 检查用户名是否可用
     * 
     * @param username 用户名
     * @return 可用返回true，不可用返回false
     */
    public boolean isUsernameAvailable(String username) {
        if (username == null || username.trim().isEmpty()) {
            return false;
        }
        
        return userDAO.findByUsername(username.trim()) == null;
    }
    
    /**
     * 获取所有用户列表
     * 
     * @return 用户列表
     */
    public List<User> getAllUsers() {
        return userDAO.findAll();
    }
    
    /**
     * 密码加密
     * 使用MD5算法对密码进行加密
     * 
     * @param password 明文密码
     * @return 加密后的密码
     */
    private String encryptPassword(String password) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(password.getBytes());
            
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            System.err.println("MD5算法不可用: " + e.getMessage());
            // 如果MD5不可用，返回原密码（不推荐在生产环境使用）
            return password;
        }
    }
    
    /**
     * 验证用户角色
     * 
     * @param user 用户对象
     * @param requiredRole 需要的角色
     * @return 验证通过返回true，失败返回false
     */
    public boolean validateUserRole(User user, Integer requiredRole) {
        if (user == null || user.getRole() == null || requiredRole == null) {
            return false;
        }
        
        return user.getRole().equals(requiredRole);
    }
    
    /**
     * 检查用户是否为管理员
     * 
     * @param user 用户对象
     * @return 是管理员返回true，否则返回false
     */
    public boolean isAdmin(User user) {
        return validateUserRole(user, User.ROLE_ADMIN);
    }
    
    /**
     * 检查用户是否为卖家
     * 
     * @param user 用户对象
     * @return 是卖家返回true，否则返回false
     */
    public boolean isSeller(User user) {
        return validateUserRole(user, User.ROLE_SELLER);
    }
    
    /**
     * 检查用户是否为买家
     * 
     * @param user 用户对象
     * @return 是买家返回true，否则返回false
     */
    public boolean isBuyer(User user) {
        return validateUserRole(user, User.ROLE_BUYER);
    }
    
    /**
     * 验证邮箱格式
     * 
     * @param email 邮箱地址
     * @return 格式正确返回true，错误返回false
     */
    public boolean isValidEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }
        
        // 简单的邮箱格式验证
        String emailRegex = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$";
        return email.matches(emailRegex);
    }
    
    /**
     * 验证手机号格式
     * 
     * @param phone 手机号
     * @return 格式正确返回true，错误返回false
     */
    public boolean isValidPhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return false;
        }
        
        // 简单的手机号格式验证（中国大陆）
        String phoneRegex = "^1[3-9]\\d{9}$";
        return phone.matches(phoneRegex);
    }
}
