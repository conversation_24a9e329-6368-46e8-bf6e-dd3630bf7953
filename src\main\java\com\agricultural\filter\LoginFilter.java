package com.agricultural.filter;

import com.agricultural.model.User;
import com.agricultural.util.ResponseUtil;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * 登录验证过滤器
 * 检查用户是否已登录，未登录的用户将被重定向到登录页面
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class LoginFilter implements Filter {
    
    // 不需要登录验证的路径
    private static final List<String> EXCLUDED_PATHS = Arrays.asList(
        "/user/login",
        "/user/register",
        "/user/checkUsername",
        "/item/list",
        "/item/detail",
        "/item/search",
        "/item/category"
    );
    
    /**
     * 过滤器初始化
     */
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        System.out.println("LoginFilter initialized");
    }
    
    /**
     * 执行过滤
     */
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        // 获取请求路径
        String requestURI = httpRequest.getRequestURI();
        String contextPath = httpRequest.getContextPath();
        String path = requestURI.substring(contextPath.length());
        
        // 检查是否为排除路径
        if (isExcludedPath(path)) {
            chain.doFilter(request, response);
            return;
        }
        
        // 检查用户是否已登录
        HttpSession session = httpRequest.getSession(false);
        User currentUser = null;
        
        if (session != null) {
            currentUser = (User) session.getAttribute("currentUser");
        }
        
        if (currentUser == null) {
            // 用户未登录
            handleUnauthorized(httpRequest, httpResponse);
            return;
        }
        
        // 用户已登录，继续执行
        chain.doFilter(request, response);
    }
    
    /**
     * 检查是否为排除路径
     */
    private boolean isExcludedPath(String path) {
        for (String excludedPath : EXCLUDED_PATHS) {
            if (path.startsWith(excludedPath)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 处理未授权访问
     */
    private void handleUnauthorized(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        
        String requestedWith = request.getHeader("X-Requested-With");
        
        // 判断是否为AJAX请求
        if ("XMLHttpRequest".equals(requestedWith)) {
            // AJAX请求，返回JSON错误信息
            ResponseUtil.sendJsonError(response, 401, "用户未登录，请先登录");
        } else {
            // 普通请求，重定向到登录页面
            String contextPath = request.getContextPath();
            String loginUrl = contextPath + "/login.jsp";
            
            // 保存原始请求URL，登录后可以跳转回来
            String originalUrl = request.getRequestURI();
            String queryString = request.getQueryString();
            if (queryString != null) {
                originalUrl += "?" + queryString;
            }
            
            // 将原始URL保存到session中
            HttpSession session = request.getSession();
            session.setAttribute("originalUrl", originalUrl);
            
            response.sendRedirect(loginUrl);
        }
    }
    
    /**
     * 过滤器销毁
     */
    @Override
    public void destroy() {
        System.out.println("LoginFilter destroyed");
    }
}
