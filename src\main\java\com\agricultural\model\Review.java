package com.agricultural.model;

import java.sql.Timestamp;

/**
 * 评价实体类
 * 对应数据库中的reviews表
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class Review {
    
    // 评价ID，主键
    private Integer reviewId;
    
    // 订单ID，外键关联订单表，唯一约束
    private Integer orderId;
    
    // 买家ID，外键关联用户表
    private Integer buyerId;
    
    // 卖家ID，外键关联用户表
    private Integer sellerId;
    
    // 评分：1-5分
    private Integer score;
    
    // 评价内容
    private String comment;
    
    // 评价时间
    private Timestamp createTime;
    
    // 关联对象（用于查询时填充）
    private User buyer;     // 买家信息
    private User seller;    // 卖家信息
    private Order order;    // 订单信息
    
    /**
     * 无参构造函数
     */
    public Review() {
    }
    
    /**
     * 带参构造函数（用于提交评价）
     * 
     * @param orderId 订单ID
     * @param buyerId 买家ID
     * @param sellerId 卖家ID
     * @param score 评分
     * @param comment 评价内容
     */
    public Review(Integer orderId, Integer buyerId, Integer sellerId, Integer score, String comment) {
        this.orderId = orderId;
        this.buyerId = buyerId;
        this.sellerId = sellerId;
        this.score = score;
        this.comment = comment;
    }
    
    // Getter和Setter方法
    
    public Integer getReviewId() {
        return reviewId;
    }
    
    public void setReviewId(Integer reviewId) {
        this.reviewId = reviewId;
    }
    
    public Integer getOrderId() {
        return orderId;
    }
    
    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }
    
    public Integer getBuyerId() {
        return buyerId;
    }
    
    public void setBuyerId(Integer buyerId) {
        this.buyerId = buyerId;
    }
    
    public Integer getSellerId() {
        return sellerId;
    }
    
    public void setSellerId(Integer sellerId) {
        this.sellerId = sellerId;
    }
    
    public Integer getScore() {
        return score;
    }
    
    public void setScore(Integer score) {
        this.score = score;
    }
    
    public String getComment() {
        return comment;
    }
    
    public void setComment(String comment) {
        this.comment = comment;
    }
    
    public Timestamp getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }
    
    public User getBuyer() {
        return buyer;
    }
    
    public void setBuyer(User buyer) {
        this.buyer = buyer;
    }
    
    public User getSeller() {
        return seller;
    }
    
    public void setSeller(User seller) {
        this.seller = seller;
    }
    
    public Order getOrder() {
        return order;
    }
    
    public void setOrder(Order order) {
        this.order = order;
    }
    
    /**
     * 获取评分描述
     * 
     * @return 评分描述字符串
     */
    public String getScoreText() {
        switch (score) {
            case 1:
                return "很差";
            case 2:
                return "较差";
            case 3:
                return "一般";
            case 4:
                return "良好";
            case 5:
                return "优秀";
            default:
                return "未知";
        }
    }
    
    /**
     * 获取星级显示
     * 
     * @return 星级字符串
     */
    public String getStarDisplay() {
        StringBuilder stars = new StringBuilder();
        for (int i = 1; i <= 5; i++) {
            if (i <= score) {
                stars.append("★");
            } else {
                stars.append("☆");
            }
        }
        return stars.toString();
    }
    
    /**
     * 检查评分是否有效
     * 
     * @return true表示有效，false表示无效
     */
    public boolean isValidScore() {
        return score != null && score >= 1 && score <= 5;
    }
    
    /**
     * 重写toString方法，方便调试
     */
    @Override
    public String toString() {
        return "Review{" +
                "reviewId=" + reviewId +
                ", orderId=" + orderId +
                ", buyerId=" + buyerId +
                ", sellerId=" + sellerId +
                ", score=" + score +
                ", comment='" + comment + '\'' +
                ", createTime=" + createTime +
                '}';
    }
    
    /**
     * 重写equals方法，用于对象比较
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        Review review = (Review) obj;
        return reviewId != null ? reviewId.equals(review.reviewId) : review.reviewId == null;
    }
    
    /**
     * 重写hashCode方法
     */
    @Override
    public int hashCode() {
        return reviewId != null ? reviewId.hashCode() : 0;
    }
}
