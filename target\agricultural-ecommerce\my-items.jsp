<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.agricultural.model.User" %>
<%
    // 检查是否已登录
    User currentUser = (User) session.getAttribute("currentUser");
    if (currentUser == null) {
        response.sendRedirect("login.jsp?redirect=" + java.net.URLEncoder.encode(request.getRequestURL().toString(), "UTF-8"));
        return;
    }
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的商品 - 农产品电商平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            padding: 8px 16px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }

        .nav-links a:hover {
            background-color: rgba(255,255,255,0.1);
        }

        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .page-title {
            font-size: 1.8rem;
            color: #2c3e50;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
            text-decoration: none;
            display: inline-block;
        }

        .btn-success {
            background-color: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background-color: #229954;
        }

        .items-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .items-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .item-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            transition: box-shadow 0.3s;
        }

        .item-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .item-status {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-available {
            background-color: #27ae60;
            color: white;
        }

        .status-offline {
            background-color: #95a5a6;
            color: white;
        }

        .status-sold {
            background-color: #e74c3c;
            color: white;
        }

        .item-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .item-description {
            color: #666;
            margin-bottom: 10px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .item-price {
            font-size: 1.3rem;
            font-weight: bold;
            color: #e74c3c;
            margin-bottom: 10px;
        }

        .item-meta {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 15px;
        }

        .item-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 14px;
        }

        .btn-primary {
            background-color: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background-color: #2980b9;
        }

        .btn-warning {
            background-color: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background-color: #e67e22;
        }

        .btn-danger {
            background-color: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background-color: #c0392b;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .empty {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        /* 编辑模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .modal-title {
            font-size: 1.2rem;
            font-weight: bold;
        }

        .close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            color: #999;
        }

        .close:hover {
            color: #333;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-group textarea {
            height: 80px;
            resize: vertical;
        }

        .form-actions {
            text-align: right;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }

        @media (max-width: 768px) {
            .nav {
                flex-direction: column;
                gap: 10px;
            }

            .page-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .items-grid {
                grid-template-columns: 1fr;
            }

            .modal-content {
                margin: 10% auto;
                width: 95%;
            }
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <header class="header">
        <nav class="nav">
            <div class="logo">🌱 农产品电商平台</div>
            <div class="nav-links">
                <a href="index.jsp">首页</a>
                <a href="cart.jsp">购物车</a>
                <a href="my-orders.jsp">我的订单</a>
                <a href="publish-item.jsp">发布商品</a>
                <span>欢迎，<%= currentUser.getUsername() %></span>
                <a href="<%=request.getContextPath()%>/user/logout">退出</a>
            </div>
        </nav>
    </header>

    <!-- 主要内容 -->
    <main class="container">
        <div class="page-header">
            <h1 class="page-title">我的商品</h1>
            <a href="publish-item.jsp" class="btn btn-success">发布新商品</a>
        </div>

        <!-- 商品列表 -->
        <div class="items-container">
            <div id="itemsContent">
                <div class="loading">正在加载商品...</div>
            </div>
        </div>
    </main>

    <!-- 编辑商品模态框 -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">编辑商品</h3>
                <span class="close" onclick="closeEditModal()">&times;</span>
            </div>
            <form id="editForm">
                <input type="hidden" id="editItemId" name="itemId">

                <div class="form-group">
                    <label for="editTitle">商品标题 *</label>
                    <input type="text" id="editTitle" name="title" required maxlength="100">
                </div>

                <div class="form-group">
                    <label for="editDescription">商品描述</label>
                    <textarea id="editDescription" name="description" maxlength="500"></textarea>
                </div>

                <div class="form-group">
                    <label for="editPrice">商品价格 *</label>
                    <input type="number" id="editPrice" name="price" required min="0" step="0.01">
                </div>

                <div class="form-group">
                    <label for="editCategory">商品分类</label>
                    <select id="editCategory" name="category">
                        <option value="蔬菜">蔬菜</option>
                        <option value="水果">水果</option>
                        <option value="粮食">粮食</option>
                        <option value="其他">其他</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="editOrigin">产地</label>
                    <input type="text" id="editOrigin" name="origin" maxlength="50">
                </div>

                <div class="form-group">
                    <label for="editStock">库存数量</label>
                    <input type="number" id="editStock" name="stock" min="0">
                </div>

                <div class="form-group">
                    <label for="editUnit">计量单位</label>
                    <input type="text" id="editUnit" name="unit" maxlength="10" placeholder="如：斤、公斤、箱等">
                </div>

                <div class="form-actions">
                    <button type="button" class="btn btn-secondary btn-small" onclick="closeEditModal()">取消</button>
                    <button type="submit" class="btn btn-primary btn-small">保存</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadMyItems();
        });

        // 加载我的商品
        function loadMyItems() {
            document.getElementById('itemsContent').innerHTML = '<div class="loading">正在加载商品...</div>';

            fetch('<%=request.getContextPath()%>/item/my')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayItems(data.data);
                    } else {
                        document.getElementById('itemsContent').innerHTML = '<div class="empty">加载商品失败: ' + data.message + '</div>';
                    }
                })
                .catch(error => {
                    console.error('加载商品失败:', error);
                    document.getElementById('itemsContent').innerHTML = '<div class="empty">加载商品失败，请稍后重试</div>';
                });
        }

        // 显示商品列表
        function displayItems(items) {
            const container = document.getElementById('itemsContent');

            if (!items || items.length === 0) {
                container.innerHTML = '<div class="empty">您还没有发布任何商品<br><a href="publish-item.jsp" class="btn btn-success" style="margin-top: 15px;">立即发布</a></div>';
                return;
            }

            const itemsHtml = items.map(item => {
                let html = '<div class="item-card">';
                html += '<div class="item-header">';
                html += '<div class="item-status ' + getStatusClass(item.status) + '">' + getStatusText(item.status) + '</div>';
                html += '</div>';
                html += '<div class="item-title">' + escapeHtml(item.title) + '</div>';
                html += '<div class="item-description">' + escapeHtml(item.description || '暂无描述') + '</div>';
                html += '<div class="item-price">¥' + item.price + '</div>';
                html += '<div class="item-meta">发布时间: ' + formatDate(item.createTime) + '</div>';
                html += '<div class="item-actions">';
                html += getItemActions(item);
                html += '</div>';
                html += '</div>';
                return html;
            }).join('');

            container.innerHTML = '<div class="items-grid">' + itemsHtml + '</div>';
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch (status) {
                case 1: return 'status-available';
                case 2: return 'status-offline';
                case 3: return 'status-sold';
                default: return '';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch (status) {
                case 1: return '上架中';
                case 2: return '已下架';
                case 3: return '已售出';
                default: return '未知状态';
            }
        }

        // 获取商品操作按钮
        function getItemActions(item) {
            let actions = [];

            if (item.status === 1) { // 上架中
                actions.push('<button class="btn btn-warning btn-small" onclick="updateItemStatus(' + item.itemId + ', 2)">下架</button>');
            } else if (item.status === 2) { // 已下架
                actions.push('<button class="btn btn-success btn-small" onclick="updateItemStatus(' + item.itemId + ', 1)">上架</button>');
            }

            if (item.status !== 3) { // 未售出的商品可以编辑和删除
                actions.push('<button class="btn btn-primary btn-small" onclick="editItem(' + item.itemId + ')">编辑</button>');
                actions.push('<button class="btn btn-danger btn-small" onclick="deleteItem(' + item.itemId + ')">删除</button>');
            }

            return actions.join('');
        }

        // 更新商品状态
        function updateItemStatus(itemId, status) {
            const statusText = status === 1 ? '上架' : '下架';

            if (confirm('确定要' + statusText + '这件商品吗？')) {
                fetch('<%=request.getContextPath()%>/item/updateStatus', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'itemId=' + itemId + '&status=' + status
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('商品' + statusText + '成功！');
                        loadMyItems();
                    } else {
                        alert(statusText + '失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error(statusText + '失败:', error);
                    alert(statusText + '失败，请稍后重试');
                });
            }
        }

        // 编辑商品
        function editItem(itemId) {
            // 首先获取商品详情
            fetch('<%=request.getContextPath()%>/item/detail/' + itemId)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showEditModal(data.data);
                    } else {
                        alert('获取商品信息失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('获取商品信息失败:', error);
                    alert('获取商品信息失败，请稍后重试');
                });
        }

        // 显示编辑模态框
        function showEditModal(item) {
            document.getElementById('editItemId').value = item.itemId;
            document.getElementById('editTitle').value = item.title || '';
            document.getElementById('editDescription').value = item.description || '';
            document.getElementById('editPrice').value = item.price || '';
            document.getElementById('editCategory').value = item.category || '其他';
            document.getElementById('editOrigin').value = item.origin || '';
            document.getElementById('editStock').value = item.stock || '';
            document.getElementById('editUnit').value = item.unit || '';

            document.getElementById('editModal').style.display = 'block';
        }

        // 关闭编辑模态框
        function closeEditModal() {
            document.getElementById('editModal').style.display = 'none';
        }

        // 处理编辑表单提交
        document.getElementById('editForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const params = new URLSearchParams();

            for (let [key, value] of formData.entries()) {
                params.append(key, value);
            }

            fetch('<%=request.getContextPath()%>/item/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: params.toString()
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('商品更新成功！');
                    closeEditModal();
                    loadMyItems();
                } else {
                    alert('更新失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('更新失败:', error);
                alert('更新失败，请稍后重试');
            });
        });

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('editModal');
            if (event.target === modal) {
                closeEditModal();
            }
        }

        // 删除商品
        function deleteItem(itemId) {
            if (confirm('确定要删除这件商品吗？删除后无法恢复。')) {
                fetch('<%=request.getContextPath()%>/item/delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'itemId=' + itemId
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('商品删除成功！');
                        loadMyItems();
                    } else {
                        alert('删除失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('删除失败:', error);
                    alert('删除失败，请稍后重试');
                });
            }
        }

        // 工具函数：HTML转义
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 工具函数：格式化日期
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-CN');
        }
    </script>
</body>
</html>
