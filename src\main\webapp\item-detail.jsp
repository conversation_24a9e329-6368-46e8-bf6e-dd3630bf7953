<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.campustrade.model.User" %>
<%
    // 获取当前登录用户
    User currentUser = (User) session.getAttribute("currentUser");

    // 获取商品ID参数
    String itemIdParam = request.getParameter("id");
    if (itemIdParam == null || itemIdParam.trim().isEmpty()) {
        response.sendRedirect("index.jsp");
        return;
    }
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品详情 - 校园二手交易平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            line-height: 1.6;
        }

        .header {
            background-color: #2c3e50;
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .nav {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            margin: 0 15px;
            padding: 8px 16px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }

        .nav-links a:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }

        .item-detail {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .item-header {
            padding: 30px;
            border-bottom: 1px solid #eee;
        }

        .item-title {
            font-size: 2rem;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .item-price {
            font-size: 2.5rem;
            color: #e74c3c;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .item-meta {
            display: flex;
            gap: 30px;
            color: #666;
            font-size: 0.9rem;
        }

        .item-content {
            padding: 30px;
        }

        .item-description {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #333;
            margin-bottom: 30px;
        }

        .seller-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }

        .seller-title {
            font-size: 1.2rem;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .seller-details {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .seller-name {
            font-weight: bold;
            color: #333;
        }

        .credit-score {
            background: #27ae60;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .item-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            padding: 20px 0;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background-color: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background-color: #2980b9;
        }

        .btn-success {
            background-color: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background-color: #229954;
        }

        .btn-secondary {
            background-color: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #7f8c8d;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .error {
            text-align: center;
            padding: 50px;
            color: #e74c3c;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-available {
            background: #27ae60;
            color: white;
        }

        .status-sold {
            background: #e74c3c;
            color: white;
        }

        .status-offline {
            background: #95a5a6;
            color: white;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 0 10px;
            }

            .item-header {
                padding: 20px;
            }

            .item-content {
                padding: 20px;
            }

            .item-title {
                font-size: 1.5rem;
            }

            .item-price {
                font-size: 2rem;
            }

            .item-meta {
                flex-direction: column;
                gap: 10px;
            }

            .seller-details {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .item-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <header class="header">
        <nav class="nav">
            <div class="logo">校园二手交易平台</div>
            <div class="nav-links">
                <a href="index.jsp">首页</a>
                <% if (currentUser != null) { %>
                    <a href="my-items.jsp">我的商品</a>
                    <a href="my-orders.jsp">我的订单</a>
                    <a href="publish-item.jsp">发布商品</a>
                <% } %>
            </div>
        </nav>
    </header>

    <!-- 主要内容 -->
    <div class="container">
        <div id="itemDetail" class="item-detail">
            <div class="loading">正在加载商品详情...</div>
        </div>
    </div>

    <script>
        const itemId = <%= itemIdParam %>;
        const currentUserId = <%= currentUser != null ? currentUser.getUserId() : "null" %>;

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadItemDetail();
        });

        // 加载商品详情
        function loadItemDetail() {
            fetch('<%=request.getContextPath()%>/item/detail/' + itemId)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayItemDetail(data.data);
                    } else {
                        document.getElementById('itemDetail').innerHTML =
                            '<div class="error">加载商品详情失败: ' + data.message + '</div>';
                    }
                })
                .catch(error => {
                    console.error('加载商品详情失败:', error);
                    document.getElementById('itemDetail').innerHTML =
                        '<div class="error">加载商品详情失败，请稍后重试</div>';
                });
        }

        // 显示商品详情
        function displayItemDetail(item) {
            const statusText = getStatusText(item.status);
            const statusClass = getStatusClass(item.status);

            let html = '<div class="item-header">';
            html += '<h1 class="item-title">' + escapeHtml(item.title) + '</h1>';
            html += '<div class="item-price">¥' + item.price + '</div>';
            html += '<div class="item-meta">';
            html += '<span>发布时间: ' + formatDate(item.createTime) + '</span>';
            html += '<span class="status-badge ' + statusClass + '">' + statusText + '</span>';
            html += '</div>';
            html += '</div>';
            html += '<div class="item-content">';
            html += '<div class="item-description">';
            html += '<h3>商品描述</h3>';
            html += '<p>' + escapeHtml(item.description || '暂无描述') + '</p>';
            html += '</div>';
            html += '<div class="seller-info">';
            html += '<h3 class="seller-title">卖家信息</h3>';
            html += '<div class="seller-details">';
            html += '<span class="seller-name">卖家: ' + escapeHtml(item.seller ? item.seller.username : '未知') + '</span>';
            if (item.seller) {
                html += '<span class="credit-score">' + item.seller.creditScore + '分</span>';
            }
            html += '</div>';
            html += '</div>';
            html += '<div class="item-actions">';
            html += generateActionButtons(item);
            html += '</div>';
            html += '</div>';

            document.getElementById('itemDetail').innerHTML = html;
        }

        // 生成操作按钮
        function generateActionButtons(item) {
            let buttons = '<a href="index.jsp" class="btn btn-secondary">返回首页</a>';

            if (currentUserId && item.status === 1) { // 商品上架状态
                if (currentUserId !== item.sellerId) {
                    // 不是卖家，显示购买按钮
                    buttons += '<button class="btn btn-success" onclick="buyItem(' + item.itemId + ')">立即购买</button>';
                } else {
                    // 是卖家，显示管理按钮
                    buttons += '<a href="my-items.jsp" class="btn btn-primary">管理我的商品</a>';
                }
            } else if (!currentUserId) {
                // 未登录用户
                buttons += '<a href="login.jsp" class="btn btn-primary">登录后购买</a>';
            }

            return buttons;
        }

        // 购买商品
        function buyItem(itemId) {
            if (confirm('确定要购买这件商品吗？')) {
                fetch('<%=request.getContextPath()%>/order/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'itemId=' + itemId
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('订单创建成功！');
                        window.location.href = 'my-orders.jsp';
                    } else {
                        alert('购买失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('购买失败:', error);
                    alert('购买失败，请稍后重试');
                });
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 1: return '上架中';
                case 2: return '已下架';
                case 3: return '已售出';
                default: return '未知状态';
            }
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch(status) {
                case 1: return 'status-available';
                case 2: return 'status-offline';
                case 3: return 'status-sold';
                default: return 'status-offline';
            }
        }

        // 工具函数：HTML转义
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 工具函数：格式化日期
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-CN');
        }
    </script>
</body>
</html>
