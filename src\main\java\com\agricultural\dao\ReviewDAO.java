package com.agricultural.dao;

import com.agricultural.model.Review;
import com.agricultural.model.User;
import com.agricultural.model.Order;
import com.agricultural.util.DBUtil;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 评价数据访问对象
 * 负责评价相关的数据库操作
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ReviewDAO {
    
    /**
     * 插入新评价
     * 
     * @param review 评价对象
     * @return 插入成功返回生成的评价ID，失败返回null
     */
    public Integer insert(Review review) {
        String sql = "INSERT INTO reviews (order_id, buyer_id, seller_id, score, comment) VALUES (?, ?, ?, ?, ?)";
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
            
            pstmt.setInt(1, review.getOrderId());
            pstmt.setInt(2, review.getBuyerId());
            pstmt.setInt(3, review.getSellerId());
            pstmt.setInt(4, review.getScore());
            pstmt.setString(5, review.getComment());
            
            int affectedRows = pstmt.executeUpdate();
            
            if (affectedRows > 0) {
                rs = pstmt.getGeneratedKeys();
                if (rs.next()) {
                    Integer reviewId = rs.getInt(1);
                    review.setReviewId(reviewId);
                    return reviewId;
                }
            }
        } catch (SQLException e) {
            System.err.println("插入评价失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, rs);
        }
        
        return null;
    }
    
    /**
     * 根据订单ID查找评价
     * 
     * @param orderId 订单ID
     * @return 找到返回评价对象，否则返回null
     */
    public Review findByOrderId(Integer orderId) {
        String sql = "SELECT r.*, " +
                    "b.username as buyer_name, " +
                    "s.username as seller_name " +
                    "FROM reviews r " +
                    "LEFT JOIN users b ON r.buyer_id = b.user_id " +
                    "LEFT JOIN users s ON r.seller_id = s.user_id " +
                    "WHERE r.order_id = ?";
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, orderId);
            
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return mapResultSetToReview(rs);
            }
        } catch (SQLException e) {
            System.err.println("根据订单ID查询评价失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, rs);
        }
        
        return null;
    }
    
    /**
     * 根据卖家ID查询评价
     * 
     * @param sellerId 卖家ID
     * @return 评价列表
     */
    public List<Review> findBySellerId(Integer sellerId) {
        String sql = "SELECT r.*, " +
                    "b.username as buyer_name, " +
                    "s.username as seller_name " +
                    "FROM reviews r " +
                    "LEFT JOIN users b ON r.buyer_id = b.user_id " +
                    "LEFT JOIN users s ON r.seller_id = s.user_id " +
                    "WHERE r.seller_id = ? ORDER BY r.create_time DESC";
        
        List<Review> reviews = new ArrayList<>();
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, sellerId);
            
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                reviews.add(mapResultSetToReview(rs));
            }
        } catch (SQLException e) {
            System.err.println("根据卖家ID查询评价失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, rs);
        }
        
        return reviews;
    }
    
    /**
     * 根据买家ID查询评价
     * 
     * @param buyerId 买家ID
     * @return 评价列表
     */
    public List<Review> findByBuyerId(Integer buyerId) {
        String sql = "SELECT r.*, " +
                    "b.username as buyer_name, " +
                    "s.username as seller_name " +
                    "FROM reviews r " +
                    "LEFT JOIN users b ON r.buyer_id = b.user_id " +
                    "LEFT JOIN users s ON r.seller_id = s.user_id " +
                    "WHERE r.buyer_id = ? ORDER BY r.create_time DESC";
        
        List<Review> reviews = new ArrayList<>();
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, buyerId);
            
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                reviews.add(mapResultSetToReview(rs));
            }
        } catch (SQLException e) {
            System.err.println("根据买家ID查询评价失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, rs);
        }
        
        return reviews;
    }
    
    /**
     * 查询所有评价
     * 
     * @return 评价列表
     */
    public List<Review> findAll() {
        String sql = "SELECT r.*, " +
                    "b.username as buyer_name, " +
                    "s.username as seller_name " +
                    "FROM reviews r " +
                    "LEFT JOIN users b ON r.buyer_id = b.user_id " +
                    "LEFT JOIN users s ON r.seller_id = s.user_id " +
                    "ORDER BY r.create_time DESC";
        
        List<Review> reviews = new ArrayList<>();
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                reviews.add(mapResultSetToReview(rs));
            }
        } catch (SQLException e) {
            System.err.println("查询所有评价失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, rs);
        }
        
        return reviews;
    }
    
    /**
     * 将ResultSet映射为Review对象
     * 
     * @param rs ResultSet对象
     * @return Review对象
     * @throws SQLException SQL异常
     */
    private Review mapResultSetToReview(ResultSet rs) throws SQLException {
        Review review = new Review();
        review.setReviewId(rs.getInt("review_id"));
        review.setOrderId(rs.getInt("order_id"));
        review.setBuyerId(rs.getInt("buyer_id"));
        review.setSellerId(rs.getInt("seller_id"));
        review.setScore(rs.getInt("score"));
        review.setComment(rs.getString("comment"));
        review.setCreateTime(rs.getTimestamp("create_time"));
        
        // 设置买家信息
        String buyerName = rs.getString("buyer_name");
        if (buyerName != null) {
            User buyer = new User();
            buyer.setUserId(review.getBuyerId());
            buyer.setUsername(buyerName);
            review.setBuyer(buyer);
        }
        
        // 设置卖家信息
        String sellerName = rs.getString("seller_name");
        if (sellerName != null) {
            User seller = new User();
            seller.setUserId(review.getSellerId());
            seller.setUsername(sellerName);
            review.setSeller(seller);
        }
        
        return review;
    }
    
    /**
     * 关闭数据库资源
     */
    private void closeResources(Connection conn, PreparedStatement pstmt, ResultSet rs) {
        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
                System.err.println("关闭ResultSet失败: " + e.getMessage());
            }
        }
        if (pstmt != null) {
            try {
                pstmt.close();
            } catch (SQLException e) {
                System.err.println("关闭PreparedStatement失败: " + e.getMessage());
            }
        }
        if (conn != null) {
            try {
                conn.close();
            } catch (SQLException e) {
                System.err.println("关闭Connection失败: " + e.getMessage());
            }
        }
    }
}
