package com.agricultural.servlet;

import com.agricultural.model.Order;
import com.agricultural.model.User;
import com.agricultural.service.OrderService;
import com.agricultural.util.ResponseUtil;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.List;

/**
 * 订单控制器
 * 处理订单相关的HTTP请求
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class OrderServlet extends HttpServlet {
    
    private OrderService orderService;
    
    /**
     * Servlet初始化
     */
    @Override
    public void init() throws ServletException {
        super.init();
        this.orderService = new OrderService();
        System.out.println("OrderServlet initialized");
    }
    
    /**
     * 处理GET请求
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        
        try {
            if (pathInfo == null || "/".equals(pathInfo)) {
                // 获取用户订单列表
                getUserOrders(request, response);
            } else if (pathInfo.startsWith("/detail/")) {
                // 获取订单详情
                getOrderDetail(request, response);
            } else if ("/buy".equals(pathInfo)) {
                // 获取购买订单
                getBuyOrders(request, response);
            } else if ("/sell".equals(pathInfo)) {
                // 获取销售订单
                getSellOrders(request, response);
            } else if ("/all".equals(pathInfo)) {
                // 获取所有订单（管理员功能）
                getAllOrders(request, response);
            } else {
                ResponseUtil.sendJsonError(response, 404, "请求的资源不存在");
            }
        } catch (Exception e) {
            System.err.println("处理GET请求失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, 500, "服务器内部错误: " + e.getMessage());
        }
    }
    
    /**
     * 处理POST请求
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        
        try {
            if ("/create".equals(pathInfo)) {
                // 创建订单
                createOrder(request, response);
            } else if ("/pay".equals(pathInfo)) {
                // 支付订单
                payOrder(request, response);
            } else if ("/ship".equals(pathInfo)) {
                // 发货
                shipOrder(request, response);
            } else if ("/confirm".equals(pathInfo)) {
                // 确认收货
                confirmOrder(request, response);
            } else if ("/cancel".equals(pathInfo)) {
                // 取消订单
                cancelOrder(request, response);
            } else {
                ResponseUtil.sendJsonError(response, 404, "请求的资源不存在");
            }
        } catch (Exception e) {
            System.err.println("处理POST请求失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, 500, "服务器内部错误: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户订单列表
     */
    private void getUserOrders(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }
        
        try {
            // 根据用户角色返回不同的订单列表
            List<Order> orders;
            if (currentUser.getRole() == User.ROLE_SELLER) {
                orders = orderService.getSellOrders(currentUser.getUserId());
            } else {
                orders = orderService.getBuyOrders(currentUser.getUserId());
            }
            
            ResponseUtil.sendJsonSuccess(response, orders);
        } catch (Exception e) {
            System.err.println("获取用户订单失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "获取用户订单失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取订单详情
     */
    private void getOrderDetail(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }
        
        String pathInfo = request.getPathInfo();
        String orderIdStr = pathInfo.substring("/detail/".length());
        
        try {
            Integer orderId = Integer.parseInt(orderIdStr);
            Order order = orderService.getOrderById(orderId);
            
            if (order == null) {
                ResponseUtil.sendJsonError(response, 404, "订单不存在");
                return;
            }
            
            // 检查权限（只有买家、卖家或管理员可以查看订单详情）
            if (!currentUser.getUserId().equals(order.getBuyerId()) && 
                !currentUser.getUserId().equals(order.getSellerId()) &&
                currentUser.getRole() != User.ROLE_ADMIN) {
                ResponseUtil.sendJsonError(response, 403, "无权限查看该订单");
                return;
            }
            
            ResponseUtil.sendJsonSuccess(response, order);
        } catch (NumberFormatException e) {
            ResponseUtil.sendJsonError(response, 400, "订单ID格式错误");
        } catch (Exception e) {
            System.err.println("获取订单详情失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "获取订单详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取购买订单
     */
    private void getBuyOrders(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }
        
        try {
            List<Order> orders = orderService.getBuyOrders(currentUser.getUserId());
            ResponseUtil.sendJsonSuccess(response, orders);
        } catch (Exception e) {
            System.err.println("获取购买订单失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "获取购买订单失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取销售订单
     */
    private void getSellOrders(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }
        
        try {
            List<Order> orders = orderService.getSellOrders(currentUser.getUserId());
            ResponseUtil.sendJsonSuccess(response, orders);
        } catch (Exception e) {
            System.err.println("获取销售订单失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "获取销售订单失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取所有订单（管理员功能）
     */
    private void getAllOrders(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }
        
        // 检查管理员权限
        if (currentUser.getRole() != User.ROLE_ADMIN) {
            ResponseUtil.sendJsonError(response, 403, "权限不足");
            return;
        }
        
        try {
            List<Order> orders = orderService.getAllOrders();
            ResponseUtil.sendJsonSuccess(response, orders);
        } catch (Exception e) {
            System.err.println("获取所有订单失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "获取所有订单失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建订单
     */
    private void createOrder(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }
        
        String shippingAddress = request.getParameter("shippingAddress");
        
        // 参数验证
        if (shippingAddress == null || shippingAddress.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "收货地址不能为空");
            return;
        }
        
        try {
            Order order = orderService.createOrderFromCart(currentUser.getUserId(), shippingAddress);
            
            if (order != null) {
                ResponseUtil.sendJsonSuccess(response, "订单创建成功", order);
            } else {
                ResponseUtil.sendJsonError(response, "订单创建失败");
            }
        } catch (IllegalArgumentException e) {
            ResponseUtil.sendJsonError(response, e.getMessage());
        } catch (Exception e) {
            System.err.println("创建订单失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "创建订单失败: " + e.getMessage());
        }
    }
    
    /**
     * 支付订单
     */
    private void payOrder(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }
        
        String orderIdStr = request.getParameter("orderId");
        
        if (orderIdStr == null || orderIdStr.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "订单ID不能为空");
            return;
        }
        
        try {
            Integer orderId = Integer.parseInt(orderIdStr);
            boolean success = orderService.payOrder(orderId, currentUser.getUserId());
            
            if (success) {
                ResponseUtil.sendJsonSuccess(response, "支付成功", null);
            } else {
                ResponseUtil.sendJsonError(response, "支付失败");
            }
        } catch (NumberFormatException e) {
            ResponseUtil.sendJsonError(response, "订单ID格式错误");
        } catch (IllegalArgumentException e) {
            ResponseUtil.sendJsonError(response, e.getMessage());
        } catch (Exception e) {
            System.err.println("支付订单失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "支付订单失败: " + e.getMessage());
        }
    }
    
    /**
     * 发货
     */
    private void shipOrder(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }
        
        String orderIdStr = request.getParameter("orderId");
        String logisticsCompany = request.getParameter("logisticsCompany");
        String trackingNumber = request.getParameter("trackingNumber");
        
        if (orderIdStr == null || orderIdStr.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "订单ID不能为空");
            return;
        }
        
        try {
            Integer orderId = Integer.parseInt(orderIdStr);
            boolean success = orderService.shipOrder(orderId, currentUser.getUserId(), 
                logisticsCompany, trackingNumber);
            
            if (success) {
                ResponseUtil.sendJsonSuccess(response, "发货成功", null);
            } else {
                ResponseUtil.sendJsonError(response, "发货失败");
            }
        } catch (NumberFormatException e) {
            ResponseUtil.sendJsonError(response, "订单ID格式错误");
        } catch (IllegalArgumentException e) {
            ResponseUtil.sendJsonError(response, e.getMessage());
        } catch (Exception e) {
            System.err.println("发货失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "发货失败: " + e.getMessage());
        }
    }
    
    /**
     * 确认收货
     */
    private void confirmOrder(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }
        
        String orderIdStr = request.getParameter("orderId");
        
        if (orderIdStr == null || orderIdStr.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "订单ID不能为空");
            return;
        }
        
        try {
            Integer orderId = Integer.parseInt(orderIdStr);
            boolean success = orderService.confirmOrder(orderId, currentUser.getUserId());
            
            if (success) {
                ResponseUtil.sendJsonSuccess(response, "确认收货成功", null);
            } else {
                ResponseUtil.sendJsonError(response, "确认收货失败");
            }
        } catch (NumberFormatException e) {
            ResponseUtil.sendJsonError(response, "订单ID格式错误");
        } catch (IllegalArgumentException e) {
            ResponseUtil.sendJsonError(response, e.getMessage());
        } catch (Exception e) {
            System.err.println("确认收货失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "确认收货失败: " + e.getMessage());
        }
    }
    
    /**
     * 取消订单
     */
    private void cancelOrder(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }
        
        String orderIdStr = request.getParameter("orderId");
        
        if (orderIdStr == null || orderIdStr.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "订单ID不能为空");
            return;
        }
        
        try {
            Integer orderId = Integer.parseInt(orderIdStr);
            boolean success = orderService.cancelOrder(orderId, currentUser.getUserId());
            
            if (success) {
                ResponseUtil.sendJsonSuccess(response, "取消订单成功", null);
            } else {
                ResponseUtil.sendJsonError(response, "取消订单失败");
            }
        } catch (NumberFormatException e) {
            ResponseUtil.sendJsonError(response, "订单ID格式错误");
        } catch (IllegalArgumentException e) {
            ResponseUtil.sendJsonError(response, e.getMessage());
        } catch (Exception e) {
            System.err.println("取消订单失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "取消订单失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取当前登录用户
     */
    private User getCurrentUser(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            return (User) session.getAttribute("currentUser");
        }
        return null;
    }
}
