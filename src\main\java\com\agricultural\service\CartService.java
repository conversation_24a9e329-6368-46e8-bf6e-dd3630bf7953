package com.agricultural.service;

import com.agricultural.dao.CartDAO;
import com.agricultural.dao.ItemDAO;
import com.agricultural.model.CartItem;
import com.agricultural.model.Item;

import java.math.BigDecimal;
import java.util.List;

/**
 * 购物车业务逻辑服务类
 * 处理购物车相关的业务逻辑
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class CartService {
    
    private CartDAO cartDAO;
    private ItemDAO itemDAO;
    
    /**
     * 构造函数
     */
    public CartService() {
        this.cartDAO = new CartDAO();
        this.itemDAO = new ItemDAO();
    }
    
    /**
     * 添加商品到购物车
     * 
     * @param userId 用户ID
     * @param itemId 商品ID
     * @param quantity 数量
     * @return 添加成功返回购物车ID，失败返回null
     */
    public Integer addToCart(Integer userId, Integer itemId, Integer quantity) {
        // 参数验证
        if (userId == null || userId <= 0) {
            throw new IllegalArgumentException("用户ID无效");
        }
        if (itemId == null || itemId <= 0) {
            throw new IllegalArgumentException("商品ID无效");
        }
        if (quantity == null || quantity <= 0) {
            throw new IllegalArgumentException("商品数量必须大于0");
        }
        
        // 检查商品是否存在且可购买
        Item item = itemDAO.findById(itemId);
        if (item == null) {
            throw new IllegalArgumentException("商品不存在");
        }
        if (!item.isAvailable()) {
            throw new IllegalArgumentException("商品不可购买");
        }
        if (item.getStock() < quantity) {
            throw new IllegalArgumentException("库存不足");
        }
        
        // 检查是否为自己的商品
        if (userId.equals(item.getSellerId())) {
            throw new IllegalArgumentException("不能购买自己发布的商品");
        }
        
        // 创建购物车商品对象
        CartItem cartItem = new CartItem(userId, itemId, quantity);
        
        // 添加到购物车
        return cartDAO.insert(cartItem);
    }
    
    /**
     * 获取用户购物车商品列表
     * 
     * @param userId 用户ID
     * @return 购物车商品列表
     */
    public List<CartItem> getCartItems(Integer userId) {
        if (userId == null || userId <= 0) {
            throw new IllegalArgumentException("用户ID无效");
        }
        
        return cartDAO.findByUserId(userId);
    }
    
    /**
     * 更新购物车商品数量
     * 
     * @param cartId 购物车ID
     * @param quantity 新数量
     * @return 更新成功返回true，失败返回false
     */
    public boolean updateQuantity(Integer cartId, Integer quantity) {
        if (cartId == null || cartId <= 0) {
            throw new IllegalArgumentException("购物车ID无效");
        }
        if (quantity == null || quantity <= 0) {
            throw new IllegalArgumentException("商品数量必须大于0");
        }
        
        return cartDAO.updateQuantity(cartId, quantity);
    }
    
    /**
     * 删除购物车商品
     * 
     * @param cartId 购物车ID
     * @return 删除成功返回true，失败返回false
     */
    public boolean removeFromCart(Integer cartId) {
        if (cartId == null || cartId <= 0) {
            throw new IllegalArgumentException("购物车ID无效");
        }
        
        return cartDAO.delete(cartId);
    }
    
    /**
     * 清空用户购物车
     * 
     * @param userId 用户ID
     * @return 清空成功返回true，失败返回false
     */
    public boolean clearCart(Integer userId) {
        if (userId == null || userId <= 0) {
            throw new IllegalArgumentException("用户ID无效");
        }
        
        return cartDAO.clearByUserId(userId);
    }
    
    /**
     * 计算购物车总金额
     * 
     * @param userId 用户ID
     * @return 购物车总金额
     */
    public BigDecimal calculateTotalAmount(Integer userId) {
        List<CartItem> cartItems = getCartItems(userId);
        BigDecimal totalAmount = BigDecimal.ZERO;
        
        for (CartItem cartItem : cartItems) {
            if (cartItem.isItemAvailable()) {
                totalAmount = totalAmount.add(cartItem.getSubtotal());
            }
        }
        
        return totalAmount;
    }
    
    /**
     * 获取购物车商品总数
     * 
     * @param userId 用户ID
     * @return 购物车商品总数
     */
    public int getCartItemCount(Integer userId) {
        List<CartItem> cartItems = getCartItems(userId);
        int totalCount = 0;
        
        for (CartItem cartItem : cartItems) {
            if (cartItem.isItemAvailable()) {
                totalCount += cartItem.getQuantity();
            }
        }
        
        return totalCount;
    }
    
    /**
     * 验证购物车商品是否可以结算
     * 
     * @param userId 用户ID
     * @return 可以结算返回true，否则返回false
     */
    public boolean validateCartForCheckout(Integer userId) {
        List<CartItem> cartItems = getCartItems(userId);
        
        if (cartItems.isEmpty()) {
            return false;
        }
        
        for (CartItem cartItem : cartItems) {
            if (!cartItem.isItemAvailable()) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 检查商品是否已在购物车中
     * 
     * @param userId 用户ID
     * @param itemId 商品ID
     * @return 存在返回true，不存在返回false
     */
    public boolean isItemInCart(Integer userId, Integer itemId) {
        if (userId == null || userId <= 0 || itemId == null || itemId <= 0) {
            return false;
        }
        
        return cartDAO.existsByUserAndItem(userId, itemId);
    }
    
    /**
     * 获取购物车摘要信息
     * 
     * @param userId 用户ID
     * @return 购物车摘要信息
     */
    public CartSummary getCartSummary(Integer userId) {
        List<CartItem> cartItems = getCartItems(userId);
        
        CartSummary summary = new CartSummary();
        summary.itemCount = 0;
        summary.totalQuantity = 0;
        summary.totalAmount = BigDecimal.ZERO;
        summary.availableItemCount = 0;
        
        for (CartItem cartItem : cartItems) {
            summary.itemCount++;
            summary.totalQuantity += cartItem.getQuantity();
            
            if (cartItem.isItemAvailable()) {
                summary.availableItemCount++;
                summary.totalAmount = summary.totalAmount.add(cartItem.getSubtotal());
            }
        }
        
        return summary;
    }
    
    /**
     * 购物车摘要信息内部类
     */
    public static class CartSummary {
        public int itemCount = 0;           // 商品种类数
        public int totalQuantity = 0;       // 商品总数量
        public BigDecimal totalAmount = BigDecimal.ZERO;  // 总金额
        public int availableItemCount = 0;  // 可用商品种类数
        
        @Override
        public String toString() {
            return "CartSummary{" +
                    "itemCount=" + itemCount +
                    ", totalQuantity=" + totalQuantity +
                    ", totalAmount=" + totalAmount +
                    ", availableItemCount=" + availableItemCount +
                    '}';
        }
    }
}
