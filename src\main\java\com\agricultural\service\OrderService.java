package com.agricultural.service;

import com.agricultural.dao.OrderDAO;
import com.agricultural.dao.OrderItemDAO;
import com.agricultural.dao.CartDAO;
import com.agricultural.dao.ItemDAO;
import com.agricultural.model.Order;
import com.agricultural.model.OrderItem;
import com.agricultural.model.CartItem;
import com.agricultural.model.Item;
import com.agricultural.util.DBUtil;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * 订单业务逻辑服务类
 * 处理订单相关的业务逻辑
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class OrderService {
    
    private OrderDAO orderDAO;
    private OrderItemDAO orderItemDAO;
    private CartDAO cartDAO;
    private ItemDAO itemDAO;
    
    /**
     * 构造函数
     */
    public OrderService() {
        this.orderDAO = new OrderDAO();
        this.orderItemDAO = new OrderItemDAO();
        this.cartDAO = new CartDAO();
        this.itemDAO = new ItemDAO();
    }
    
    /**
     * 从购物车创建订单
     * 
     * @param buyerId 买家ID
     * @param shippingAddress 收货地址
     * @return 创建成功返回订单对象，失败返回null
     */
    public Order createOrderFromCart(Integer buyerId, String shippingAddress) {
        // 参数验证
        if (buyerId == null || buyerId <= 0) {
            throw new IllegalArgumentException("买家ID无效");
        }
        if (shippingAddress == null || shippingAddress.trim().isEmpty()) {
            throw new IllegalArgumentException("收货地址不能为空");
        }
        
        Connection conn = null;
        try {
            // 开启事务
            conn = DBUtil.getConnection();
            conn.setAutoCommit(false);
            
            // 1. 获取购物车商品
            List<CartItem> cartItems = cartDAO.findByUserId(buyerId);
            if (cartItems.isEmpty()) {
                throw new IllegalArgumentException("购物车为空");
            }
            
            // 2. 验证购物车商品并获取卖家信息
            CartItem firstItem = cartItems.get(0);
            if (firstItem.getItem() == null) {
                throw new IllegalArgumentException("购物车中存在无效商品，请刷新后重试");
            }
            if (firstItem.getItem().getSeller() == null) {
                throw new IllegalArgumentException("商品卖家信息缺失，请联系客服");
            }
            Integer sellerId = firstItem.getItem().getSeller().getUserId();

            // 3. 验证库存并计算订单总价
            BigDecimal totalPrice = BigDecimal.ZERO;
            for (CartItem cartItem : cartItems) {
                Item item = cartItem.getItem();
                if (item == null) {
                    throw new IllegalArgumentException("购物车中存在无效商品，请刷新后重试");
                }

                // 重新从数据库获取最新的商品信息以确保库存准确
                Item latestItem = itemDAO.findById(item.getItemId());
                if (latestItem == null) {
                    throw new IllegalArgumentException("商品 " + item.getTitle() + " 已不存在");
                }

                // 检查商品状态
                if (!latestItem.isAvailable()) {
                    if (latestItem.getStatus() == Item.STATUS_OFFLINE) {
                        throw new IllegalArgumentException("商品 " + latestItem.getTitle() + " 已下架");
                    } else if (latestItem.getStatus() == Item.STATUS_OUT_OF_STOCK) {
                        throw new IllegalArgumentException("商品 " + latestItem.getTitle() + " 已缺货");
                    } else {
                        throw new IllegalArgumentException("商品 " + latestItem.getTitle() + " 不可购买");
                    }
                }

                // 检查库存是否充足
                if (latestItem.getStock() == null || latestItem.getStock() < cartItem.getQuantity()) {
                    throw new IllegalArgumentException("商品 " + latestItem.getTitle() + " 库存不足，当前库存：" +
                        (latestItem.getStock() != null ? latestItem.getStock() : 0) + "件");
                }

                // 更新购物车中的商品信息为最新信息
                cartItem.setItem(latestItem);
                totalPrice = totalPrice.add(cartItem.getSubtotal());
            }
            
            // 4. 创建订单
            Order order = new Order(buyerId, sellerId, totalPrice);
            order.setShippingAddress(shippingAddress.trim());
            Integer orderId = orderDAO.insert(order);
            
            if (orderId == null) {
                throw new RuntimeException("创建订单失败");
            }
            order.setOrderId(orderId);
            
            // 5. 创建订单商品
            List<OrderItem> orderItems = new ArrayList<>();
            for (CartItem cartItem : cartItems) {
                OrderItem orderItem = new OrderItem(orderId, cartItem.getItemId(), 
                    cartItem.getQuantity(), cartItem.getItem().getPrice());
                orderItems.add(orderItem);
                
                // 减少库存
                Item item = cartItem.getItem();
                if (!item.reduceStock(cartItem.getQuantity())) {
                    throw new IllegalArgumentException("商品 " + item.getTitle() + " 库存不足");
                }
                itemDAO.update(item);
            }
            
            // 批量插入订单商品
            if (!orderItemDAO.batchInsert(orderItems)) {
                throw new RuntimeException("创建订单商品失败");
            }
            
            // 6. 清空购物车
            cartDAO.clearByUserId(buyerId);
            
            // 提交事务
            conn.commit();
            
            return order;
            
        } catch (IllegalArgumentException e) {
            // 回滚事务
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException rollbackEx) {
                    System.err.println("回滚事务失败: " + rollbackEx.getMessage());
                }
            }
            // 直接抛出业务异常，不包装
            throw e;
        } catch (Exception e) {
            // 回滚事务
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException rollbackEx) {
                    System.err.println("回滚事务失败: " + rollbackEx.getMessage());
                }
            }
            // 记录详细错误信息
            System.err.println("创建订单失败: " + e.getClass().getSimpleName() + " - " + e.getMessage());
            e.printStackTrace();

            String errorMessage = e.getMessage();
            if (errorMessage == null || errorMessage.trim().isEmpty()) {
                errorMessage = "系统内部错误，请稍后重试";
            }
            throw new RuntimeException("创建订单失败: " + errorMessage, e);
        } finally {
            if (conn != null) {
                try {
                    conn.setAutoCommit(true);
                    conn.close();
                } catch (SQLException e) {
                    System.err.println("关闭连接失败: " + e.getMessage());
                }
            }
        }
    }
    
    /**
     * 支付订单（模拟支付）
     * 
     * @param orderId 订单ID
     * @param userId 操作用户ID（必须是买家）
     * @return 支付成功返回true，失败返回false
     */
    public boolean payOrder(Integer orderId, Integer userId) {
        if (orderId == null || orderId <= 0) {
            throw new IllegalArgumentException("订单ID无效");
        }
        if (userId == null || userId <= 0) {
            throw new IllegalArgumentException("用户ID无效");
        }
        
        // 查询订单
        Order order = orderDAO.findById(orderId);
        if (order == null) {
            throw new IllegalArgumentException("订单不存在");
        }
        
        // 检查操作权限（只有买家可以支付订单）
        if (!userId.equals(order.getBuyerId())) {
            throw new IllegalArgumentException("无权限操作该订单");
        }
        
        // 检查订单状态
        if (!order.canPay()) {
            throw new IllegalArgumentException("订单状态不允许支付");
        }
        
        // 更新订单状态为已支付
        return orderDAO.updateStatus(orderId, Order.STATUS_PAID);
    }
    
    /**
     * 发货
     * 
     * @param orderId 订单ID
     * @param userId 操作用户ID（必须是卖家）
     * @param logisticsCompany 物流公司
     * @param trackingNumber 物流单号
     * @return 发货成功返回true，失败返回false
     */
    public boolean shipOrder(Integer orderId, Integer userId, String logisticsCompany, String trackingNumber) {
        if (orderId == null || orderId <= 0) {
            throw new IllegalArgumentException("订单ID无效");
        }
        if (userId == null || userId <= 0) {
            throw new IllegalArgumentException("用户ID无效");
        }
        
        // 查询订单
        Order order = orderDAO.findById(orderId);
        if (order == null) {
            throw new IllegalArgumentException("订单不存在");
        }
        
        // 检查操作权限（只有卖家可以发货）
        if (!userId.equals(order.getSellerId())) {
            throw new IllegalArgumentException("无权限操作该订单");
        }
        
        // 检查订单状态
        if (!order.canShip()) {
            throw new IllegalArgumentException("订单状态不允许发货");
        }
        
        // 更新订单状态和物流信息
        boolean statusUpdated = orderDAO.updateStatus(orderId, Order.STATUS_SHIPPED);
        boolean logisticsUpdated = orderDAO.updateLogistics(orderId, logisticsCompany, trackingNumber, "已发货");
        
        return statusUpdated && logisticsUpdated;
    }
    
    /**
     * 确认收货
     * 
     * @param orderId 订单ID
     * @param userId 操作用户ID（必须是买家）
     * @return 确认成功返回true，失败返回false
     */
    public boolean confirmOrder(Integer orderId, Integer userId) {
        if (orderId == null || orderId <= 0) {
            throw new IllegalArgumentException("订单ID无效");
        }
        if (userId == null || userId <= 0) {
            throw new IllegalArgumentException("用户ID无效");
        }
        
        // 查询订单
        Order order = orderDAO.findById(orderId);
        if (order == null) {
            throw new IllegalArgumentException("订单不存在");
        }
        
        // 检查操作权限（只有买家可以确认收货）
        if (!userId.equals(order.getBuyerId())) {
            throw new IllegalArgumentException("无权限操作该订单");
        }
        
        // 检查订单状态
        if (order.getStatus() != Order.STATUS_SHIPPED) {
            throw new IllegalArgumentException("订单状态不允许确认收货");
        }
        
        // 更新订单状态为已完成
        return orderDAO.updateStatus(orderId, Order.STATUS_COMPLETED);
    }
    
    /**
     * 取消订单
     * 
     * @param orderId 订单ID
     * @param userId 操作用户ID
     * @return 取消成功返回true，失败返回false
     */
    public boolean cancelOrder(Integer orderId, Integer userId) {
        if (orderId == null || orderId <= 0) {
            throw new IllegalArgumentException("订单ID无效");
        }
        if (userId == null || userId <= 0) {
            throw new IllegalArgumentException("用户ID无效");
        }
        
        // 查询订单
        Order order = orderDAO.findById(orderId);
        if (order == null) {
            throw new IllegalArgumentException("订单不存在");
        }
        
        // 检查操作权限（买家和卖家都可以取消订单）
        if (!userId.equals(order.getBuyerId()) && !userId.equals(order.getSellerId())) {
            throw new IllegalArgumentException("无权限操作该订单");
        }
        
        // 检查订单状态
        if (!order.canCancel()) {
            throw new IllegalArgumentException("订单状态不允许取消");
        }
        
        // 更新订单状态为已取消
        return orderDAO.updateStatus(orderId, Order.STATUS_CANCELLED);
    }
    
    /**
     * 获取用户的购买订单
     *
     * @param buyerId 买家ID
     * @return 订单列表
     */
    public List<Order> getBuyOrders(Integer buyerId) {
        if (buyerId == null || buyerId <= 0) {
            throw new IllegalArgumentException("买家ID无效");
        }

        List<Order> orders = orderDAO.findByBuyerId(buyerId);
        // 为每个订单加载订单商品信息
        for (Order order : orders) {
            List<OrderItem> orderItems = orderItemDAO.findByOrderId(order.getOrderId());
            order.setOrderItems(orderItems);
        }
        return orders;
    }

    /**
     * 获取用户的销售订单
     *
     * @param sellerId 卖家ID
     * @return 订单列表
     */
    public List<Order> getSellOrders(Integer sellerId) {
        if (sellerId == null || sellerId <= 0) {
            throw new IllegalArgumentException("卖家ID无效");
        }

        List<Order> orders = orderDAO.findBySellerId(sellerId);
        // 为每个订单加载订单商品信息
        for (Order order : orders) {
            List<OrderItem> orderItems = orderItemDAO.findByOrderId(order.getOrderId());
            order.setOrderItems(orderItems);
        }
        return orders;
    }
    
    /**
     * 根据订单ID获取订单详情
     * 
     * @param orderId 订单ID
     * @return 订单对象
     */
    public Order getOrderById(Integer orderId) {
        if (orderId == null || orderId <= 0) {
            return null;
        }
        
        Order order = orderDAO.findById(orderId);
        if (order != null) {
            // 获取订单商品
            List<OrderItem> orderItems = orderItemDAO.findByOrderId(orderId);
            order.setOrderItems(orderItems);
        }
        
        return order;
    }
    
    /**
     * 获取所有订单列表（管理员功能）
     *
     * @return 订单列表
     */
    public List<Order> getAllOrders() {
        List<Order> orders = orderDAO.findAll();
        // 为每个订单加载订单商品信息
        for (Order order : orders) {
            List<OrderItem> orderItems = orderItemDAO.findByOrderId(order.getOrderId());
            order.setOrderItems(orderItems);
        }
        return orders;
    }
}
