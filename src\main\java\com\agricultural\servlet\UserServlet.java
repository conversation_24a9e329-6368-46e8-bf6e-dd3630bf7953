package com.agricultural.servlet;

import com.agricultural.model.User;
import com.agricultural.service.UserService;
import com.agricultural.util.DBUtil;
import com.agricultural.util.ResponseUtil;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.List;

/**
 * 用户控制器
 * 处理用户相关的HTTP请求
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class UserServlet extends HttpServlet {
    
    private UserService userService;
    
    /**
     * Servlet初始化
     */
    @Override
    public void init() throws ServletException {
        super.init();
        this.userService = new UserService();
        
        // 初始化数据库连接池
        DBUtil.initDataSource(getServletContext());
        System.out.println("UserServlet initialized");
    }
    
    /**
     * 处理GET请求
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        
        try {
            if (pathInfo == null || "/".equals(pathInfo)) {
                // 获取当前用户信息
                getCurrentUser(request, response);
            } else if ("/profile".equals(pathInfo)) {
                // 获取用户详细信息
                getUserProfile(request, response);
            } else if ("/list".equals(pathInfo)) {
                // 获取用户列表（管理员功能）
                getUserList(request, response);
            } else if ("/checkUsername".equals(pathInfo)) {
                // 检查用户名是否可用
                checkUsername(request, response);
            } else if ("/logout".equals(pathInfo)) {
                // 用户登出
                logout(request, response);
            } else {
                ResponseUtil.sendJsonError(response, 404, "请求的资源不存在");
            }
        } catch (Exception e) {
            System.err.println("处理GET请求失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, 500, "服务器内部错误: " + e.getMessage());
        }
    }
    
    /**
     * 处理POST请求
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        
        try {
            if ("/register".equals(pathInfo)) {
                // 用户注册
                register(request, response);
            } else if ("/login".equals(pathInfo)) {
                // 用户登录
                login(request, response);
            } else if ("/update".equals(pathInfo)) {
                // 更新用户信息
                updateUser(request, response);
            } else {
                ResponseUtil.sendJsonError(response, 404, "请求的资源不存在");
            }
        } catch (Exception e) {
            System.err.println("处理POST请求失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, 500, "服务器内部错误: " + e.getMessage());
        }
    }
    
    /**
     * 用户注册
     */
    private void register(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String username = request.getParameter("username");
        String password = request.getParameter("password");
        String email = request.getParameter("email");
        String phone = request.getParameter("phone");
        String roleStr = request.getParameter("role");
        String address = request.getParameter("address");
        
        // 参数验证
        if (username == null || username.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "用户名不能为空");
            return;
        }
        if (password == null || password.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "密码不能为空");
            return;
        }
        if (email == null || email.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "邮箱不能为空");
            return;
        }
        
        try {
            // 解析角色
            Integer role = User.ROLE_BUYER; // 默认为买家
            if (roleStr != null && !roleStr.trim().isEmpty()) {
                try {
                    role = Integer.parseInt(roleStr);
                } catch (NumberFormatException e) {
                    role = User.ROLE_BUYER;
                }
            }
            
            // 验证邮箱格式
            if (!userService.isValidEmail(email)) {
                ResponseUtil.sendJsonError(response, "邮箱格式不正确");
                return;
            }
            
            // 验证手机号格式
            if (phone != null && !phone.trim().isEmpty() && !userService.isValidPhone(phone)) {
                ResponseUtil.sendJsonError(response, "手机号格式不正确");
                return;
            }
            
            // 注册用户
            User user = userService.register(username, password, email, phone, role, address);
            
            if (user != null) {
                // 注册成功，自动登录
                HttpSession session = request.getSession();
                session.setAttribute("currentUser", user);
                
                ResponseUtil.sendJsonSuccess(response, "注册成功", user);
            } else {
                ResponseUtil.sendJsonError(response, "注册失败");
            }
        } catch (IllegalArgumentException e) {
            ResponseUtil.sendJsonError(response, e.getMessage());
        } catch (Exception e) {
            System.err.println("用户注册失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "注册失败: " + e.getMessage());
        }
    }
    
    /**
     * 用户登录
     */
    private void login(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String username = request.getParameter("username");
        String password = request.getParameter("password");
        
        // 参数验证
        if (username == null || username.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "用户名不能为空");
            return;
        }
        if (password == null || password.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "密码不能为空");
            return;
        }
        
        try {
            // 用户登录
            User user = userService.login(username, password);
            
            if (user != null) {
                // 登录成功，保存到session
                HttpSession session = request.getSession();
                session.setAttribute("currentUser", user);
                
                ResponseUtil.sendJsonSuccess(response, "登录成功", user);
            } else {
                ResponseUtil.sendJsonError(response, "用户名或密码错误");
            }
        } catch (IllegalArgumentException e) {
            ResponseUtil.sendJsonError(response, e.getMessage());
        } catch (Exception e) {
            System.err.println("用户登录失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "登录失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取当前用户信息
     */
    private void getCurrentUser(HttpServletRequest request, HttpServletResponse response) throws IOException {
        HttpSession session = request.getSession(false);
        if (session != null) {
            User currentUser = (User) session.getAttribute("currentUser");
            if (currentUser != null) {
                ResponseUtil.sendJsonSuccess(response, currentUser);
                return;
            }
        }
        
        ResponseUtil.sendJsonError(response, 401, "用户未登录");
    }
    
    /**
     * 获取用户详细信息
     */
    private void getUserProfile(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }
        
        try {
            // 重新从数据库获取最新的用户信息
            User user = userService.getUserById(currentUser.getUserId());
            if (user != null) {
                ResponseUtil.sendJsonSuccess(response, user);
            } else {
                ResponseUtil.sendJsonError(response, "用户不存在");
            }
        } catch (Exception e) {
            System.err.println("获取用户信息失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "获取用户信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查用户名是否可用
     */
    private void checkUsername(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String username = request.getParameter("username");
        
        if (username == null || username.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "用户名不能为空");
            return;
        }
        
        try {
            boolean available = userService.isUsernameAvailable(username);
            ResponseUtil.sendJsonSuccess(response, "检查完成", available);
        } catch (Exception e) {
            System.err.println("检查用户名失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "检查用户名失败: " + e.getMessage());
        }
    }
    
    /**
     * 用户登出
     */
    private void logout(HttpServletRequest request, HttpServletResponse response) throws IOException {
        HttpSession session = request.getSession(false);
        if (session != null) {
            session.invalidate();
        }
        
        ResponseUtil.sendJsonSuccess(response, "登出成功", null);
    }
    
    /**
     * 获取用户列表（管理员功能）
     */
    private void getUserList(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }
        
        // 检查管理员权限
        if (!userService.isAdmin(currentUser)) {
            ResponseUtil.sendJsonError(response, 403, "权限不足");
            return;
        }
        
        try {
            List<User> users = userService.getAllUsers();
            ResponseUtil.sendJsonSuccess(response, users);
        } catch (Exception e) {
            System.err.println("获取用户列表失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "获取用户列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新用户信息
     */
    private void updateUser(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }
        
        String email = request.getParameter("email");
        String phone = request.getParameter("phone");
        String address = request.getParameter("address");
        
        try {
            // 更新用户信息
            if (email != null && !email.trim().isEmpty()) {
                if (!userService.isValidEmail(email)) {
                    ResponseUtil.sendJsonError(response, "邮箱格式不正确");
                    return;
                }
                currentUser.setEmail(email.trim());
            }
            
            if (phone != null && !phone.trim().isEmpty()) {
                if (!userService.isValidPhone(phone)) {
                    ResponseUtil.sendJsonError(response, "手机号格式不正确");
                    return;
                }
                currentUser.setPhone(phone.trim());
            }
            
            if (address != null) {
                currentUser.setAddress(address.trim());
            }
            
            boolean success = userService.updateUser(currentUser);
            if (success) {
                // 更新session中的用户信息
                HttpSession session = request.getSession();
                session.setAttribute("currentUser", currentUser);
                
                ResponseUtil.sendJsonSuccess(response, "更新成功", currentUser);
            } else {
                ResponseUtil.sendJsonError(response, "更新失败");
            }
        } catch (Exception e) {
            System.err.println("更新用户信息失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "更新用户信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取当前登录用户
     */
    private User getCurrentUser(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            return (User) session.getAttribute("currentUser");
        }
        return null;
    }
    
    /**
     * Servlet销毁
     */
    @Override
    public void destroy() {
        // 关闭数据库连接池
        DBUtil.closeDataSource();
        System.out.println("UserServlet destroyed");
        super.destroy();
    }
}
