package com.agricultural.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

/**
 * 订单实体类
 * 对应数据库中的orders表
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class Order {
    
    // 订单状态常量
    public static final int STATUS_PENDING = 1;    // 待支付
    public static final int STATUS_PAID = 2;       // 已支付
    public static final int STATUS_SHIPPED = 3;    // 已发货
    public static final int STATUS_COMPLETED = 4;  // 已完成
    public static final int STATUS_CANCELLED = 5;  // 已取消
    
    // 订单ID，主键
    private Integer orderId;
    
    // 买家ID，外键关联用户表
    private Integer buyerId;
    
    // 卖家ID，外键关联用户表
    private Integer sellerId;
    
    // 订单状态：1-待支付，2-已支付，3-已发货，4-已完成，5-已取消
    private Integer status;
    
    // 订单总价
    private BigDecimal totalPrice;
    
    // 收货地址
    private String shippingAddress;
    
    // 物流状态
    private String logisticsStatus;
    
    // 物流公司
    private String logisticsCompany;
    
    // 物流单号
    private String trackingNumber;
    
    // 创建时间
    private Timestamp createTime;
    
    // 更新时间
    private Timestamp updateTime;
    
    // 关联对象（用于查询时填充）
    private User buyer;     // 买家信息
    private User seller;    // 卖家信息
    private List<OrderItem> orderItems; // 订单商品列表
    
    /**
     * 无参构造函数
     */
    public Order() {
    }
    
    /**
     * 带参构造函数（用于创建订单）
     * 
     * @param buyerId 买家ID
     * @param sellerId 卖家ID
     * @param totalPrice 订单总价
     */
    public Order(Integer buyerId, Integer sellerId, BigDecimal totalPrice) {
        this.buyerId = buyerId;
        this.sellerId = sellerId;
        this.totalPrice = totalPrice;
        this.status = STATUS_PENDING; // 默认待支付状态
    }
    
    // Getter和Setter方法
    
    public Integer getOrderId() {
        return orderId;
    }
    
    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }
    
    public Integer getBuyerId() {
        return buyerId;
    }
    
    public void setBuyerId(Integer buyerId) {
        this.buyerId = buyerId;
    }
    
    public Integer getSellerId() {
        return sellerId;
    }
    
    public void setSellerId(Integer sellerId) {
        this.sellerId = sellerId;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public BigDecimal getTotalPrice() {
        return totalPrice;
    }
    
    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }
    
    public String getShippingAddress() {
        return shippingAddress;
    }
    
    public void setShippingAddress(String shippingAddress) {
        this.shippingAddress = shippingAddress;
    }
    
    public String getLogisticsStatus() {
        return logisticsStatus;
    }
    
    public void setLogisticsStatus(String logisticsStatus) {
        this.logisticsStatus = logisticsStatus;
    }
    
    public String getLogisticsCompany() {
        return logisticsCompany;
    }
    
    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }
    
    public String getTrackingNumber() {
        return trackingNumber;
    }
    
    public void setTrackingNumber(String trackingNumber) {
        this.trackingNumber = trackingNumber;
    }
    
    public Timestamp getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }
    
    public Timestamp getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }
    
    public User getBuyer() {
        return buyer;
    }
    
    public void setBuyer(User buyer) {
        this.buyer = buyer;
    }
    
    public User getSeller() {
        return seller;
    }
    
    public void setSeller(User seller) {
        this.seller = seller;
    }
    
    public List<OrderItem> getOrderItems() {
        return orderItems;
    }
    
    public void setOrderItems(List<OrderItem> orderItems) {
        this.orderItems = orderItems;
    }
    
    /**
     * 获取状态描述
     * 
     * @return 状态描述字符串
     */
    public String getStatusText() {
        switch (status) {
            case STATUS_PENDING:
                return "待支付";
            case STATUS_PAID:
                return "已支付";
            case STATUS_SHIPPED:
                return "已发货";
            case STATUS_COMPLETED:
                return "已完成";
            case STATUS_CANCELLED:
                return "已取消";
            default:
                return "未知状态";
        }
    }
    
    /**
     * 检查订单是否可以取消
     * 
     * @return true表示可以取消，false表示不可取消
     */
    public boolean canCancel() {
        return STATUS_PENDING == status || STATUS_PAID == status;
    }
    
    /**
     * 检查订单是否可以支付
     * 
     * @return true表示可以支付，false表示不可支付
     */
    public boolean canPay() {
        return STATUS_PENDING == status;
    }
    
    /**
     * 检查订单是否可以发货
     * 
     * @return true表示可以发货，false表示不可发货
     */
    public boolean canShip() {
        return STATUS_PAID == status;
    }
    
    /**
     * 检查订单是否已完成
     * 
     * @return true表示已完成，false表示未完成
     */
    public boolean isCompleted() {
        return STATUS_COMPLETED == status;
    }
    
    /**
     * 检查订单是否可以评价
     * 只有已完成的订单才能评价
     * 
     * @return true表示可以评价，false表示不可评价
     */
    public boolean canReview() {
        return STATUS_COMPLETED == status;
    }
    
    /**
     * 重写toString方法，方便调试
     */
    @Override
    public String toString() {
        return "Order{" +
                "orderId=" + orderId +
                ", buyerId=" + buyerId +
                ", sellerId=" + sellerId +
                ", status=" + status +
                ", totalPrice=" + totalPrice +
                ", shippingAddress='" + shippingAddress + '\'' +
                ", logisticsStatus='" + logisticsStatus + '\'' +
                ", logisticsCompany='" + logisticsCompany + '\'' +
                ", trackingNumber='" + trackingNumber + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
    
    /**
     * 重写equals方法，用于对象比较
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        Order order = (Order) obj;
        return orderId != null ? orderId.equals(order.orderId) : order.orderId == null;
    }
    
    /**
     * 重写hashCode方法
     */
    @Override
    public int hashCode() {
        return orderId != null ? orderId.hashCode() : 0;
    }
}
