package com.agricultural.dao;

import com.agricultural.model.Item;
import com.agricultural.model.User;
import com.agricultural.util.DBUtil;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 商品数据访问对象
 * 负责商品相关的数据库操作
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ItemDAO {
    
    /**
     * 插入新商品
     * 
     * @param item 商品对象
     * @return 插入成功返回生成的商品ID，失败返回null
     */
    public Integer insert(Item item) {
        String sql = "INSERT INTO items (seller_id, title, description, price, category, origin, stock, unit, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
            
            // 设置参数
            pstmt.setInt(1, item.getSellerId());
            pstmt.setString(2, item.getTitle());
            pstmt.setString(3, item.getDescription());
            pstmt.setBigDecimal(4, item.getPrice());
            pstmt.setString(5, item.getCategory());
            pstmt.setString(6, item.getOrigin());
            pstmt.setInt(7, item.getStock() != null ? item.getStock() : 1);
            pstmt.setString(8, item.getUnit());
            pstmt.setInt(9, item.getStatus() != null ? item.getStatus() : Item.STATUS_AVAILABLE);
            
            // 执行插入
            int affectedRows = pstmt.executeUpdate();
            
            if (affectedRows > 0) {
                // 获取生成的主键
                rs = pstmt.getGeneratedKeys();
                if (rs.next()) {
                    Integer itemId = rs.getInt(1);
                    item.setItemId(itemId);
                    return itemId;
                }
            }
        } catch (SQLException e) {
            System.err.println("插入商品失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, rs);
        }
        
        return null;
    }
    
    /**
     * 根据商品ID查找商品
     * 
     * @param itemId 商品ID
     * @return 找到返回商品对象，否则返回null
     */
    public Item findById(Integer itemId) {
        String sql = "SELECT i.*, u.username, u.credit_score " +
                    "FROM items i LEFT JOIN users u ON i.seller_id = u.user_id " +
                    "WHERE i.item_id = ?";
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, itemId);
            
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return mapResultSetToItem(rs);
            }
        } catch (SQLException e) {
            System.err.println("根据ID查询商品失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, rs);
        }
        
        return null;
    }
    
    /**
     * 查询所有上架的商品
     * 
     * @return 商品列表
     */
    public List<Item> findAvailableItems() {
        String sql = "SELECT i.*, u.username, u.credit_score " +
                    "FROM items i LEFT JOIN users u ON i.seller_id = u.user_id " +
                    "WHERE i.status = ? ORDER BY i.create_time DESC";
        
        List<Item> items = new ArrayList<>();
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, Item.STATUS_AVAILABLE);
            
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                items.add(mapResultSetToItem(rs));
            }
        } catch (SQLException e) {
            System.err.println("查询上架商品失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, rs);
        }
        
        return items;
    }
    
    /**
     * 根据分类查询商品
     * 
     * @param category 商品分类
     * @return 商品列表
     */
    public List<Item> findByCategory(String category) {
        String sql = "SELECT i.*, u.username, u.credit_score " +
                    "FROM items i LEFT JOIN users u ON i.seller_id = u.user_id " +
                    "WHERE i.category = ? AND i.status = ? ORDER BY i.create_time DESC";
        
        List<Item> items = new ArrayList<>();
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, category);
            pstmt.setInt(2, Item.STATUS_AVAILABLE);
            
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                items.add(mapResultSetToItem(rs));
            }
        } catch (SQLException e) {
            System.err.println("根据分类查询商品失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, rs);
        }
        
        return items;
    }
    
    /**
     * 根据卖家ID查询商品
     * 
     * @param sellerId 卖家ID
     * @return 商品列表
     */
    public List<Item> findBySellerId(Integer sellerId) {
        String sql = "SELECT i.*, u.username, u.credit_score " +
                    "FROM items i LEFT JOIN users u ON i.seller_id = u.user_id " +
                    "WHERE i.seller_id = ? ORDER BY i.create_time DESC";
        
        List<Item> items = new ArrayList<>();
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, sellerId);
            
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                items.add(mapResultSetToItem(rs));
            }
        } catch (SQLException e) {
            System.err.println("根据卖家ID查询商品失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, rs);
        }
        
        return items;
    }
    
    /**
     * 搜索商品
     * 
     * @param keyword 搜索关键词
     * @return 商品列表
     */
    public List<Item> searchByKeyword(String keyword) {
        String sql = "SELECT i.*, u.username, u.credit_score " +
                    "FROM items i LEFT JOIN users u ON i.seller_id = u.user_id " +
                    "WHERE i.status = ? AND (i.title LIKE ? OR i.description LIKE ?) " +
                    "ORDER BY i.create_time DESC";
        
        List<Item> items = new ArrayList<>();
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            
            String searchPattern = "%" + keyword + "%";
            pstmt.setInt(1, Item.STATUS_AVAILABLE);
            pstmt.setString(2, searchPattern);
            pstmt.setString(3, searchPattern);
            
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                items.add(mapResultSetToItem(rs));
            }
        } catch (SQLException e) {
            System.err.println("搜索商品失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, rs);
        }
        
        return items;
    }
    
    /**
     * 更新商品信息
     * 
     * @param item 商品对象
     * @return 更新成功返回true，失败返回false
     */
    public boolean update(Item item) {
        String sql = "UPDATE items SET title = ?, description = ?, price = ?, category = ?, origin = ?, stock = ?, unit = ?, status = ? WHERE item_id = ?";
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            
            pstmt.setString(1, item.getTitle());
            pstmt.setString(2, item.getDescription());
            pstmt.setBigDecimal(3, item.getPrice());
            pstmt.setString(4, item.getCategory());
            pstmt.setString(5, item.getOrigin());
            pstmt.setInt(6, item.getStock());
            pstmt.setString(7, item.getUnit());
            pstmt.setInt(8, item.getStatus());
            pstmt.setInt(9, item.getItemId());
            
            int affectedRows = pstmt.executeUpdate();
            return affectedRows > 0;
        } catch (SQLException e) {
            System.err.println("更新商品失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, null);
        }
        
        return false;
    }

    /**
     * 删除商品
     *
     * @param itemId 商品ID
     * @return 删除成功返回true，失败返回false
     */
    public boolean delete(Integer itemId) {
        String sql = "DELETE FROM items WHERE item_id = ?";

        Connection conn = null;
        PreparedStatement pstmt = null;

        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, itemId);

            int affectedRows = pstmt.executeUpdate();
            return affectedRows > 0;
        } catch (SQLException e) {
            System.err.println("删除商品失败: " + e.getMessage());
        } finally {
            closeResources(conn, pstmt, null);
        }

        return false;
    }

    /**
     * 将ResultSet映射为Item对象
     * 
     * @param rs ResultSet对象
     * @return Item对象
     * @throws SQLException SQL异常
     */
    private Item mapResultSetToItem(ResultSet rs) throws SQLException {
        Item item = new Item();
        item.setItemId(rs.getInt("item_id"));
        item.setSellerId(rs.getInt("seller_id"));
        item.setTitle(rs.getString("title"));
        item.setDescription(rs.getString("description"));
        item.setPrice(rs.getBigDecimal("price"));
        item.setCategory(rs.getString("category"));
        item.setOrigin(rs.getString("origin"));
        item.setStock(rs.getInt("stock"));
        item.setUnit(rs.getString("unit"));
        item.setStatus(rs.getInt("status"));
        item.setCreateTime(rs.getTimestamp("create_time"));
        item.setUpdateTime(rs.getTimestamp("update_time"));
        
        // 设置卖家信息
        String sellerName = rs.getString("username");
        if (sellerName != null) {
            User seller = new User();
            seller.setUserId(item.getSellerId());
            seller.setUsername(sellerName);
            seller.setCreditScore(rs.getInt("credit_score"));
            item.setSeller(seller);
        }
        
        return item;
    }
    
    /**
     * 关闭数据库资源
     */
    private void closeResources(Connection conn, PreparedStatement pstmt, ResultSet rs) {
        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
                System.err.println("关闭ResultSet失败: " + e.getMessage());
            }
        }
        if (pstmt != null) {
            try {
                pstmt.close();
            } catch (SQLException e) {
                System.err.println("关闭PreparedStatement失败: " + e.getMessage());
            }
        }
        if (conn != null) {
            try {
                conn.close();
            } catch (SQLException e) {
                System.err.println("关闭Connection失败: " + e.getMessage());
            }
        }
    }
}
