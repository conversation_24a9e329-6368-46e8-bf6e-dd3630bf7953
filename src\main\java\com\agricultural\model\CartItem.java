package com.agricultural.model;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * 购物车商品实体类
 * 对应数据库中的cart_items表
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class CartItem {
    
    // 购物车ID，主键
    private Integer cartId;
    
    // 用户ID，外键关联用户表
    private Integer userId;
    
    // 商品ID，外键关联商品表
    private Integer itemId;
    
    // 商品数量
    private Integer quantity;
    
    // 添加时间
    private Timestamp createTime;
    
    // 关联对象（用于查询时填充）
    private Item item;      // 商品信息
    private User user;      // 用户信息
    
    /**
     * 无参构造函数
     */
    public CartItem() {
    }
    
    /**
     * 带参构造函数
     * 
     * @param userId 用户ID
     * @param itemId 商品ID
     * @param quantity 商品数量
     */
    public CartItem(Integer userId, Integer itemId, Integer quantity) {
        this.userId = userId;
        this.itemId = itemId;
        this.quantity = quantity;
    }
    
    // Getter和Setter方法
    
    public Integer getCartId() {
        return cartId;
    }
    
    public void setCartId(Integer cartId) {
        this.cartId = cartId;
    }
    
    public Integer getUserId() {
        return userId;
    }
    
    public void setUserId(Integer userId) {
        this.userId = userId;
    }
    
    public Integer getItemId() {
        return itemId;
    }
    
    public void setItemId(Integer itemId) {
        this.itemId = itemId;
    }
    
    public Integer getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }
    
    public Timestamp getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }
    
    public Item getItem() {
        return item;
    }
    
    public void setItem(Item item) {
        this.item = item;
    }
    
    public User getUser() {
        return user;
    }
    
    public void setUser(User user) {
        this.user = user;
    }
    
    /**
     * 计算小计金额
     * 
     * @return 小计金额
     */
    public BigDecimal getSubtotal() {
        if (item != null && item.getPrice() != null && quantity != null) {
            return item.getPrice().multiply(new BigDecimal(quantity));
        }
        return BigDecimal.ZERO;
    }
    
    /**
     * 检查商品是否可用
     * 
     * @return true表示可用，false表示不可用
     */
    public boolean isItemAvailable() {
        return item != null && item.isAvailable() && 
               item.getStock() != null && item.getStock() >= quantity;
    }
    
    /**
     * 重写toString方法，方便调试
     */
    @Override
    public String toString() {
        return "CartItem{" +
                "cartId=" + cartId +
                ", userId=" + userId +
                ", itemId=" + itemId +
                ", quantity=" + quantity +
                ", createTime=" + createTime +
                '}';
    }
    
    /**
     * 重写equals方法，用于对象比较
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        CartItem cartItem = (CartItem) obj;
        return cartId != null ? cartId.equals(cartItem.cartId) : cartItem.cartId == null;
    }
    
    /**
     * 重写hashCode方法
     */
    @Override
    public int hashCode() {
        return cartId != null ? cartId.hashCode() : 0;
    }
}
