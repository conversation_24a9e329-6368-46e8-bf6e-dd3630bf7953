package com.agricultural.servlet;

import com.agricultural.dao.ItemDAO;
import com.agricultural.model.Item;
import com.agricultural.model.User;
import com.agricultural.util.ResponseUtil;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商品控制器
 * 处理商品相关的HTTP请求
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ItemServlet extends HttpServlet {
    
    private ItemDAO itemDAO;
    
    /**
     * Servlet初始化
     */
    @Override
    public void init() throws ServletException {
        super.init();
        this.itemDAO = new ItemDAO();
        System.out.println("ItemServlet initialized");
    }
    
    /**
     * 处理GET请求
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        
        try {
            if (pathInfo == null || "/".equals(pathInfo) || "/list".equals(pathInfo)) {
                // 获取商品列表
                getItemList(request, response);
            } else if (pathInfo.startsWith("/detail/")) {
                // 获取商品详情
                getItemDetail(request, response);
            } else if ("/my".equals(pathInfo)) {
                // 获取我的商品
                getMyItems(request, response);
            } else if ("/search".equals(pathInfo)) {
                // 搜索商品
                searchItems(request, response);
            } else if ("/category".equals(pathInfo)) {
                // 按分类获取商品
                getItemsByCategory(request, response);
            } else {
                ResponseUtil.sendJsonError(response, 404, "请求的资源不存在");
            }
        } catch (Exception e) {
            System.err.println("处理GET请求失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, 500, "服务器内部错误: " + e.getMessage());
        }
    }
    
    /**
     * 处理POST请求
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        
        try {
            if ("/publish".equals(pathInfo)) {
                // 发布商品
                publishItem(request, response);
            } else if ("/update".equals(pathInfo)) {
                // 更新商品
                updateItem(request, response);
            } else if ("/updateStatus".equals(pathInfo)) {
                // 更新商品状态
                updateItemStatus(request, response);
            } else if ("/delete".equals(pathInfo)) {
                // 删除商品
                deleteItem(request, response);
            } else {
                ResponseUtil.sendJsonError(response, 404, "请求的资源不存在");
            }
        } catch (Exception e) {
            System.err.println("处理POST请求失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, 500, "服务器内部错误: " + e.getMessage());
        }
    }
    
    /**
     * 获取商品列表
     */
    private void getItemList(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            List<Item> items = itemDAO.findAvailableItems();
            ResponseUtil.sendJsonSuccess(response, items);
        } catch (Exception e) {
            System.err.println("获取商品列表失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "获取商品列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取商品详情
     */
    private void getItemDetail(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String pathInfo = request.getPathInfo();
        String itemIdStr = pathInfo.substring("/detail/".length());
        
        try {
            Integer itemId = Integer.parseInt(itemIdStr);
            Item item = itemDAO.findById(itemId);
            
            if (item != null) {
                ResponseUtil.sendJsonSuccess(response, item);
            } else {
                ResponseUtil.sendJsonError(response, 404, "商品不存在");
            }
        } catch (NumberFormatException e) {
            ResponseUtil.sendJsonError(response, 400, "商品ID格式错误");
        } catch (Exception e) {
            System.err.println("获取商品详情失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "获取商品详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取我的商品
     */
    private void getMyItems(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }
        
        try {
            List<Item> items = itemDAO.findBySellerId(currentUser.getUserId());
            ResponseUtil.sendJsonSuccess(response, items);
        } catch (Exception e) {
            System.err.println("获取我的商品失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "获取我的商品失败: " + e.getMessage());
        }
    }
    
    /**
     * 搜索商品
     */
    private void searchItems(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String keyword = request.getParameter("keyword");
        
        if (keyword == null || keyword.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "搜索关键词不能为空");
            return;
        }
        
        try {
            List<Item> items = itemDAO.searchByKeyword(keyword.trim());
            ResponseUtil.sendJsonSuccess(response, items);
        } catch (Exception e) {
            System.err.println("搜索商品失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "搜索商品失败: " + e.getMessage());
        }
    }
    
    /**
     * 按分类获取商品
     */
    private void getItemsByCategory(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String category = request.getParameter("category");
        
        if (category == null || category.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "商品分类不能为空");
            return;
        }
        
        try {
            List<Item> items = itemDAO.findByCategory(category.trim());
            ResponseUtil.sendJsonSuccess(response, items);
        } catch (Exception e) {
            System.err.println("按分类获取商品失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "按分类获取商品失败: " + e.getMessage());
        }
    }
    
    /**
     * 发布商品
     */
    private void publishItem(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }
        
        // 检查是否为卖家
        if (currentUser.getRole() != User.ROLE_SELLER && currentUser.getRole() != User.ROLE_ADMIN) {
            ResponseUtil.sendJsonError(response, 403, "只有卖家可以发布商品");
            return;
        }
        
        String title = request.getParameter("title");
        String description = request.getParameter("description");
        String priceStr = request.getParameter("price");
        String category = request.getParameter("category");
        String origin = request.getParameter("origin");
        String stockStr = request.getParameter("stock");
        String unit = request.getParameter("unit");
        
        // 参数验证
        if (title == null || title.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "商品标题不能为空");
            return;
        }
        if (priceStr == null || priceStr.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "商品价格不能为空");
            return;
        }
        
        try {
            BigDecimal price = new BigDecimal(priceStr);
            if (price.compareTo(BigDecimal.ZERO) <= 0) {
                ResponseUtil.sendJsonError(response, "商品价格必须大于0");
                return;
            }
            
            Integer stock = 1; // 默认库存
            if (stockStr != null && !stockStr.trim().isEmpty()) {
                stock = Integer.parseInt(stockStr);
                if (stock < 0) {
                    ResponseUtil.sendJsonError(response, "库存数量不能为负数");
                    return;
                }
            }
            
            // 创建商品对象
            Item item = new Item(currentUser.getUserId(), title.trim(), description, price);
            if (category != null && !category.trim().isEmpty()) {
                item.setCategory(category.trim());
            }
            if (origin != null && !origin.trim().isEmpty()) {
                item.setOrigin(origin.trim());
            }
            item.setStock(stock);
            if (unit != null && !unit.trim().isEmpty()) {
                item.setUnit(unit.trim());
            }
            
            // 保存到数据库
            Integer itemId = itemDAO.insert(item);
            
            if (itemId != null) {
                item.setItemId(itemId);
                ResponseUtil.sendJsonSuccess(response, "商品发布成功", item);
            } else {
                ResponseUtil.sendJsonError(response, "商品发布失败");
            }
        } catch (NumberFormatException e) {
            ResponseUtil.sendJsonError(response, "价格或库存格式错误");
        } catch (Exception e) {
            System.err.println("发布商品失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "发布商品失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新商品
     */
    private void updateItem(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }
        
        String itemIdStr = request.getParameter("itemId");
        if (itemIdStr == null || itemIdStr.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "商品ID不能为空");
            return;
        }
        
        try {
            Integer itemId = Integer.parseInt(itemIdStr);
            Item item = itemDAO.findById(itemId);
            
            if (item == null) {
                ResponseUtil.sendJsonError(response, "商品不存在");
                return;
            }
            
            // 检查权限（只有商品所有者可以更新）
            if (!currentUser.getUserId().equals(item.getSellerId())) {
                ResponseUtil.sendJsonError(response, 403, "无权限操作该商品");
                return;
            }
            
            // 更新商品信息
            String title = request.getParameter("title");
            String description = request.getParameter("description");
            String priceStr = request.getParameter("price");
            String category = request.getParameter("category");
            String origin = request.getParameter("origin");
            String stockStr = request.getParameter("stock");
            String unit = request.getParameter("unit");
            
            if (title != null && !title.trim().isEmpty()) {
                item.setTitle(title.trim());
            }
            if (description != null) {
                item.setDescription(description);
            }
            if (priceStr != null && !priceStr.trim().isEmpty()) {
                BigDecimal price = new BigDecimal(priceStr);
                if (price.compareTo(BigDecimal.ZERO) <= 0) {
                    ResponseUtil.sendJsonError(response, "商品价格必须大于0");
                    return;
                }
                item.setPrice(price);
            }
            if (category != null) {
                item.setCategory(category.trim());
            }
            if (origin != null) {
                item.setOrigin(origin.trim());
            }
            if (stockStr != null && !stockStr.trim().isEmpty()) {
                Integer stock = Integer.parseInt(stockStr);
                if (stock < 0) {
                    ResponseUtil.sendJsonError(response, "库存数量不能为负数");
                    return;
                }
                item.setStock(stock);
            }
            if (unit != null) {
                item.setUnit(unit.trim());
            }
            
            boolean success = itemDAO.update(item);
            if (success) {
                ResponseUtil.sendJsonSuccess(response, "商品更新成功", item);
            } else {
                ResponseUtil.sendJsonError(response, "商品更新失败");
            }
        } catch (NumberFormatException e) {
            ResponseUtil.sendJsonError(response, "商品ID、价格或库存格式错误");
        } catch (Exception e) {
            System.err.println("更新商品失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "更新商品失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新商品状态
     */
    private void updateItemStatus(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }
        
        String itemIdStr = request.getParameter("itemId");
        String statusStr = request.getParameter("status");
        
        if (itemIdStr == null || itemIdStr.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "商品ID不能为空");
            return;
        }
        if (statusStr == null || statusStr.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "商品状态不能为空");
            return;
        }
        
        try {
            Integer itemId = Integer.parseInt(itemIdStr);
            Integer status = Integer.parseInt(statusStr);
            
            Item item = itemDAO.findById(itemId);
            if (item == null) {
                ResponseUtil.sendJsonError(response, "商品不存在");
                return;
            }
            
            // 检查权限（只有商品所有者可以更新状态）
            if (!currentUser.getUserId().equals(item.getSellerId())) {
                ResponseUtil.sendJsonError(response, 403, "无权限操作该商品");
                return;
            }
            
            item.setStatus(status);
            boolean success = itemDAO.update(item);
            
            if (success) {
                ResponseUtil.sendJsonSuccess(response, "商品状态更新成功", item);
            } else {
                ResponseUtil.sendJsonError(response, "商品状态更新失败");
            }
        } catch (NumberFormatException e) {
            ResponseUtil.sendJsonError(response, "商品ID或状态格式错误");
        } catch (Exception e) {
            System.err.println("更新商品状态失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "更新商品状态失败: " + e.getMessage());
        }
    }

    /**
     * 删除商品
     */
    private void deleteItem(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }

        String itemIdStr = request.getParameter("itemId");

        if (itemIdStr == null || itemIdStr.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "商品ID不能为空");
            return;
        }

        try {
            Integer itemId = Integer.parseInt(itemIdStr);
            Item item = itemDAO.findById(itemId);

            if (item == null) {
                ResponseUtil.sendJsonError(response, "商品不存在");
                return;
            }

            // 检查权限（只有商品所有者可以删除）
            if (!currentUser.getUserId().equals(item.getSellerId())) {
                ResponseUtil.sendJsonError(response, 403, "无权限操作该商品");
                return;
            }

            boolean success = itemDAO.delete(itemId);

            if (success) {
                ResponseUtil.sendJsonSuccess(response, "商品删除成功", null);
            } else {
                ResponseUtil.sendJsonError(response, "商品删除失败");
            }
        } catch (NumberFormatException e) {
            ResponseUtil.sendJsonError(response, "商品ID格式错误");
        } catch (Exception e) {
            System.err.println("删除商品失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "删除商品失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前登录用户
     */
    private User getCurrentUser(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            return (User) session.getAttribute("currentUser");
        }
        return null;
    }
}
