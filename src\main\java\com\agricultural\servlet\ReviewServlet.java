package com.agricultural.servlet;

import com.agricultural.dao.ReviewDAO;
import com.agricultural.dao.OrderDAO;
import com.agricultural.model.Review;
import com.agricultural.model.Order;
import com.agricultural.model.User;
import com.agricultural.util.ResponseUtil;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.List;

/**
 * 评价控制器
 * 处理评价相关的HTTP请求
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ReviewServlet extends HttpServlet {
    
    private ReviewDAO reviewDAO;
    private OrderDAO orderDAO;
    
    /**
     * Servlet初始化
     */
    @Override
    public void init() throws ServletException {
        super.init();
        this.reviewDAO = new ReviewDAO();
        this.orderDAO = new OrderDAO();
        System.out.println("ReviewServlet initialized");
    }
    
    /**
     * 处理GET请求
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        
        try {
            if (pathInfo == null || "/".equals(pathInfo)) {
                // 获取评价列表
                getReviews(request, response);
            } else if (pathInfo.startsWith("/order/")) {
                // 根据订单ID获取评价
                getReviewByOrderId(request, response);
            } else {
                ResponseUtil.sendJsonError(response, 404, "请求的资源不存在");
            }
        } catch (Exception e) {
            System.err.println("处理GET请求失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, 500, "服务器内部错误: " + e.getMessage());
        }
    }
    
    /**
     * 处理POST请求
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        
        try {
            if ("/submit".equals(pathInfo)) {
                // 提交评价
                submitReview(request, response);
            } else {
                ResponseUtil.sendJsonError(response, 404, "请求的资源不存在");
            }
        } catch (Exception e) {
            System.err.println("处理POST请求失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, 500, "服务器内部错误: " + e.getMessage());
        }
    }
    
    /**
     * 获取评价列表
     */
    private void getReviews(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String sellerIdStr = request.getParameter("sellerId");
        
        try {
            List<Review> reviews;
            if (sellerIdStr != null && !sellerIdStr.trim().isEmpty()) {
                Integer sellerId = Integer.parseInt(sellerIdStr);
                reviews = reviewDAO.findBySellerId(sellerId);
            } else {
                reviews = reviewDAO.findAll();
            }
            
            ResponseUtil.sendJsonSuccess(response, reviews);
        } catch (NumberFormatException e) {
            ResponseUtil.sendJsonError(response, "卖家ID格式错误");
        } catch (Exception e) {
            System.err.println("获取评价列表失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "获取评价列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据订单ID获取评价
     */
    private void getReviewByOrderId(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String pathInfo = request.getPathInfo();
        String orderIdStr = pathInfo.substring("/order/".length());
        
        try {
            Integer orderId = Integer.parseInt(orderIdStr);
            Review review = reviewDAO.findByOrderId(orderId);
            
            if (review != null) {
                ResponseUtil.sendJsonSuccess(response, review);
            } else {
                ResponseUtil.sendJsonError(response, 404, "评价不存在");
            }
        } catch (NumberFormatException e) {
            ResponseUtil.sendJsonError(response, "订单ID格式错误");
        } catch (Exception e) {
            System.err.println("获取评价失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "获取评价失败: " + e.getMessage());
        }
    }
    
    /**
     * 提交评价
     */
    private void submitReview(HttpServletRequest request, HttpServletResponse response) throws IOException {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            ResponseUtil.sendJsonError(response, 401, "用户未登录");
            return;
        }
        
        String orderIdStr = request.getParameter("orderId");
        String scoreStr = request.getParameter("score");
        String comment = request.getParameter("comment");
        
        // 参数验证
        if (orderIdStr == null || orderIdStr.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "订单ID不能为空");
            return;
        }
        if (scoreStr == null || scoreStr.trim().isEmpty()) {
            ResponseUtil.sendJsonError(response, "评分不能为空");
            return;
        }
        
        try {
            Integer orderId = Integer.parseInt(orderIdStr);
            Integer score = Integer.parseInt(scoreStr);
            
            if (score < 1 || score > 5) {
                ResponseUtil.sendJsonError(response, "评分必须在1-5之间");
                return;
            }
            
            // 检查订单是否存在且已完成
            Order order = orderDAO.findById(orderId);
            if (order == null) {
                ResponseUtil.sendJsonError(response, "订单不存在");
                return;
            }
            
            // 检查权限（只有买家可以评价）
            if (!currentUser.getUserId().equals(order.getBuyerId())) {
                ResponseUtil.sendJsonError(response, 403, "无权限评价该订单");
                return;
            }
            
            // 检查订单状态
            if (!order.canReview()) {
                ResponseUtil.sendJsonError(response, "订单状态不允许评价");
                return;
            }
            
            // 检查是否已经评价过
            Review existingReview = reviewDAO.findByOrderId(orderId);
            if (existingReview != null) {
                ResponseUtil.sendJsonError(response, "该订单已经评价过了");
                return;
            }
            
            // 创建评价
            Review review = new Review(orderId, order.getBuyerId(), order.getSellerId(), score, comment);
            Integer reviewId = reviewDAO.insert(review);
            
            if (reviewId != null) {
                review.setReviewId(reviewId);
                ResponseUtil.sendJsonSuccess(response, "评价提交成功", review);
            } else {
                ResponseUtil.sendJsonError(response, "评价提交失败");
            }
        } catch (NumberFormatException e) {
            ResponseUtil.sendJsonError(response, "订单ID或评分格式错误");
        } catch (Exception e) {
            System.err.println("提交评价失败: " + e.getMessage());
            ResponseUtil.sendJsonError(response, "提交评价失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取当前登录用户
     */
    private User getCurrentUser(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            return (User) session.getAttribute("currentUser");
        }
        return null;
    }
}
